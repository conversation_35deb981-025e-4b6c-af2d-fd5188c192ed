
# Voice Analytics Dashboard Tech Stack

This document provides a detailed overview of the technologies and components used in the Voice Analytics Dashboard project.

## Core Technologies

### Next.js
- **Version**: Latest (13+)
- **Description**: Next.js is the core framework used for this project. It provides server-side rendering, routing, and optimized performance out of the box.
- **Usage**: We're using the App Router for more intuitive and powerful routing capabilities.

### React
- **Version**: Latest stable version (shipped with Next.js)
- **Description**: React is the foundation of our UI, allowing us to build reusable components and manage state efficiently.

### TypeScript
- **Version**: Latest stable version
- **Description**: TypeScript adds static typing to our JavaScript, enhancing code quality and developer experience.

## UI and Styling

### Tailwind CSS
- **Version**: Latest stable version
- **Description**: Tailwind is our utility-first CSS framework, allowing for rapid UI development and consistent styling.
- **Usage**: Used throughout the project for styling components and layouts.

### shadcn/ui
- **Description**: A collection of re-usable components built with Radix <PERSON> and Tailwind CSS.
- **Components Used**:
  - Card
  - Select
  - Button
  - Popover
  - DropdownMenu

### Lucide React
- **Description**: A library of simply beautiful open-source icons.
- **Usage**: Used for various icons throughout the dashboard.

## Data Visualization

### Recharts
- **Version**: Latest stable version
- **Description**: A composable charting library built on React components.
- **Usage**: Used for creating line charts, area charts, and bar charts in the dashboard.

## Theming and Dark Mode

### next-themes
- **Version**: Latest stable version
- **Description**: An abstraction for themes in Next.js applications.
- **Usage**: Implements dark mode functionality and theme switching.

## Project Structure

### Components
- `layout.tsx`: The root layout component that wraps the entire application.
- `sidebar.tsx`: The sidebar navigation component.
- `analytics.tsx`: The main dashboard component containing all analytics visualizations.
- `theme-toggle.tsx`: A component for toggling between light and dark modes.
- `metric-card.tsx`: A reusable component for displaying individual metrics.
- `line-chart.tsx`: A flexible chart component supporting line, area, and bar chart types.
- `donut-chart.tsx`: A component for rendering donut charts.

### Utility Functions
- `utils.ts`: Contains utility functions, including the `cn` function for conditional class name generation.

## Future Considerations

As the project evolves, we may consider incorporating the following:

- State Management: If the application grows more complex, we might introduce a state management library like Redux or Zustand.
- API Integration: We'll need to implement API calls to fetch real-time data, possibly using React Query or SWR for efficient data fetching and caching.
- Authentication: Implementing user authentication and authorization will be crucial for securing the dashboard.
- Testing: Adding a testing framework like Jest and React Testing Library for unit and integration tests.

This tech stack provides a solid foundation for building a modern, performant, and visually appealing voice analytics dashboard. The combination of Next.js, React, and Tailwind CSS allows for rapid development, while Recharts offers flexible data visualization options. The inclusion of TypeScript and shadcn/ui components enhances code quality and UI consistency.
