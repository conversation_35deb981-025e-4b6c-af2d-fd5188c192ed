# RetellAI Call Data Elements

This document provides a comprehensive overview of the data elements available from RetellAI's call-analyzed webhook, as implemented in our Voice Analytics Dashboard.

## Core Call Properties

### Call Identification

- `call_id` (string) - Unique identifier for the call
  - Example: `"call_bb915c78daa96954cd1a025b16c"`
- `agent_id` (string) - Identifier for the RetellAI agent that handled the call
  - Example: `"agent_dc88f52320b5362e45070c49e5"`

### Call Metadata

- `call_status` (string) - Current status of the call
  - Example: `"ended"`
- `start_timestamp` (number) - Unix timestamp (ms) when the call started
  - Example: `1736795076115`
- `end_timestamp` (number) - Unix timestamp (ms) when the call ended
  - Example: `1736795201931`
- `duration_ms` (number) - Total duration of the call in milliseconds
  - Example: `125816`

### Dynamic Variables

- `retell_llm_dynamic_variables` (object) - Custom variables used during the call
  - Example: `{ "customer_name": "<PERSON>" }`

### Transcript Data

- `transcript` (string) - Full text transcript of the conversation
  - Example: `"Agent: 'Hello <PERSON> Foolery, I understand you're interested in learning more about optional employee benefits...'"`

## Database Schema Implementation

Our Supabase database stores this data in the following structure:
sql
create table calls (
id uuid default gen_random_uuid() primary key,
call_id text unique not null,
tenant_id uuid references tenants(id),
tenant_agent_id uuid references tenant_agents(id),
retell_agent_id text not null,
call_status text not null,
start_timestamp timestamptz not null,
end_timestamp timestamptz not null,
duration_ms integer not null,
transcript text,
dynamic_variables jsonb,
raw_data jsonb not null,
created_at timestamptz default now()
);

## Analytics Views

Call data is aggregated in our analytics view:
sql
create or replace view call_analytics as
select
t.id as tenant_id,
t.name as tenant_name,
ta.id as tenant_agent_id,
ta.name as agent_name,
date_trunc('day', c.start_timestamp) as day,
count() as total_calls,
sum(duration_ms)::float / 1000 / 60 as total_minutes,
count() filter (where c.call_status = 'completed') as completed_calls,
count() filter (where c.call_status != 'completed') as incomplete_calls
from tenants t
join tenant_agents ta on ta.tenant_id = t.id
join calls c on c.tenant_agent_id = ta.id
group by 1, 2, 3, 4, 5;
)

## Common Values

### Call Status Values

- `ended` - Call has concluded
- `completed` - Call completed successfully
- `failed` - Call failed to complete
- `user_hangup` - User terminated the call

### Agent Types

- `outbound_voice` - Outbound telephone calls
- `inbound_voice` - Inbound telephone calls
- `web_call` - Browser-based calls

## Best Practices

1. **Webhook Processing**
   - Always verify the agent_id exists in tenant_agents
   - Store complete raw webhook data for audit purposes
   - Process timestamps in UTC
   - Handle missing optional fields gracefully

2. **Data Storage**
   - Index frequently queried fields (implemented in schema)
   - Maintain tenant isolation through RLS policies
   - Use proper JSON indexing for dynamic_variables

3. **Analytics**
   - Pre-aggregate common metrics in views
   - Use materialized views for heavy computations
   - Implement proper date/time handling for analytics

## Related Documentation

- [RetellAI API Documentation](https://docs.retellai.com)
- [Voice Analytics Dashboard Documentation](./README.md)
- [Database Schema Documentation](./supabase/README.md)
