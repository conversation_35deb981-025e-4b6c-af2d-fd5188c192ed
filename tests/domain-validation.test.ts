/**
 * Test suite for domain validation and duplicate prevention
 * 
 * This test suite verifies that:
 * 1. Domain normalization works correctly
 * 2. Duplicate domains are prevented
 * 3. Domain validation APIs work as expected
 * 4. Database constraints prevent invalid domains
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/testing-library/jest-dom';

// Mock test data
const testDomains = [
  'example.com',
  'EXAMPLE.COM',
  'www.example.com',
  'WWW.EXAMPLE.COM',
  'test-company.org',
  'invalid..domain',
  'toolong' + 'a'.repeat(250) + '.com',
  'short.co',
  'valid-domain-123.com'
];

const testOrganization = {
  name: 'Test Organization',
  slug: 'test-org'
};

describe('Domain Validation and Duplicate Prevention', () => {
  
  describe('Domain Normalization', () => {
    it('should normalize domains consistently', async () => {
      const testCases = [
        { input: 'EXAMPLE.COM', expected: 'example.com' },
        { input: 'www.example.com', expected: 'example.com' },
        { input: 'WWW.EXAMPLE.COM', expected: 'example.com' },
        { input: '  example.com  ', expected: 'example.com' },
        { input: 'Example.Com', expected: 'example.com' }
      ];

      for (const testCase of testCases) {
        const response = await fetch('/api/admin/domains/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ domain: testCase.input })
        });

        const result = await response.json();
        expect(result.domain).toBe(testCase.expected);
      }
    });
  });

  describe('Domain Format Validation', () => {
    it('should reject invalid domain formats', async () => {
      const invalidDomains = [
        'invalid..domain',
        'domain-with-spaces .com',
        'toolong' + 'a'.repeat(250) + '.com',
        'no-tld',
        '.com',
        'domain.',
        ''
      ];

      for (const domain of invalidDomains) {
        const response = await fetch('/api/admin/domains/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ domain })
        });

        const result = await response.json();
        expect(result.valid).toBe(false);
        expect(result.error).toBe('invalid_format');
      }
    });

    it('should accept valid domain formats', async () => {
      const validDomains = [
        'example.com',
        'test-company.org',
        'valid-domain-123.com',
        'short.co',
        'subdomain.example.com'
      ];

      for (const domain of validDomains) {
        const response = await fetch('/api/admin/domains/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ domain })
        });

        const result = await response.json();
        expect(result.valid).toBe(true);
      }
    });
  });

  describe('Duplicate Domain Prevention', () => {
    it('should prevent duplicate domains in organization creation', async () => {
      // First, create an organization with a domain
      const firstOrgResponse = await fetch('/api/admin/organizations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'First Organization',
          domains: [{ domain: 'unique-test-domain.com', verified: true, admin_approved: true }]
        })
      });

      expect(firstOrgResponse.ok).toBe(true);

      // Try to create another organization with the same domain
      const secondOrgResponse = await fetch('/api/admin/organizations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'Second Organization',
          domains: [{ domain: 'unique-test-domain.com', verified: true, admin_approved: true }]
        })
      });

      const secondResult = await secondOrgResponse.json();
      expect(secondResult.domain_errors).toBeDefined();
      expect(secondResult.domain_errors.length).toBeGreaterThan(0);
      expect(secondResult.domain_errors[0].error).toContain('already registered');
    });

    it('should prevent duplicate domains with different casing', async () => {
      const response = await fetch('/api/admin/domains/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: 'UNIQUE-TEST-DOMAIN.COM' })
      });

      const result = await response.json();
      expect(result.valid).toBe(false);
      expect(result.error).toBe('domain_exists');
    });

    it('should prevent duplicate domains with www prefix', async () => {
      const response = await fetch('/api/admin/domains/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: 'www.unique-test-domain.com' })
      });

      const result = await response.json();
      expect(result.valid).toBe(false);
      expect(result.error).toBe('domain_exists');
    });
  });

  describe('Domain Availability Check', () => {
    it('should correctly identify available domains', async () => {
      const response = await fetch('/api/admin/domains/check-availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: 'definitely-available-domain-' + Date.now() + '.com' })
      });

      const result = await response.json();
      expect(result.available).toBe(true);
    });

    it('should correctly identify taken domains', async () => {
      const response = await fetch('/api/admin/domains/check-availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: 'unique-test-domain.com' })
      });

      const result = await response.json();
      expect(result.available).toBe(false);
      expect(result.error).toBe('domain_taken');
    });
  });

  describe('API Health Checks', () => {
    it('should have healthy domain validation API', async () => {
      const response = await fetch('/api/admin/domains/validate');
      const result = await response.json();
      
      expect(response.ok).toBe(true);
      expect(result.status).toBe('healthy');
    });

    it('should have healthy domain availability API', async () => {
      const response = await fetch('/api/admin/domains/check-availability');
      const result = await response.json();
      
      expect(response.ok).toBe(true);
      expect(result.status).toBe('healthy');
    });
  });
});

// Helper function to clean up test data
async function cleanupTestData() {
  // This would typically clean up any test organizations/domains created during testing
  // Implementation depends on your test setup
}

export { cleanupTestData };
