export interface DynamicVariables {
  customer_name?: string;
  [key: string]: string | undefined;
}

export interface Call {
  call_id: string
  customer_name: string
  agent_name: string
  start_timestamp: string
  duration_seconds: number
  user_sentiment: string
  call_successful: boolean
  cost: number
  call_summary: string
  transcript: string
  customer_phone?: string
  agent_phone?: string
  end_to_end_latency?: number
  disconnection_reason?: string
  audio_url?: string
  recording_url?: string
  dynamic_variables: DynamicVariables | string | null
} 