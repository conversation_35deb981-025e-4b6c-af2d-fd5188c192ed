export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          status: string
          settings: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          status?: string
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          status?: string
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      organization_agents: {
        Row: {
          id: string
          organization_id: string
          agent_id: string
          name: string
          type: string
          status: string
          settings: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          agent_id: string
          name: string
          type: string
          status?: string
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          agent_id?: string
          name?: string
          type?: string
          status?: string
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      calls: {
        Row: {
          id: string
          call_id: string
          organization_id: string
          organization_agent_id: string
          retell_agent_id: string
          call_status: string
          start_timestamp: string
          end_timestamp: string
          duration_ms: number
          transcript: string | null
          dynamic_variables: Json | null
          raw_data: <PERSON><PERSON>
          created_at: string
        }
        Insert: {
          id?: string
          call_id: string
          organization_id: string
          organization_agent_id: string
          retell_agent_id: string
          call_status: string
          start_timestamp: string
          end_timestamp: string
          duration_ms: number
          transcript?: string | null
          dynamic_variables?: Json | null
          raw_data: Json
          created_at?: string
        }
        Update: {
          id?: string
          call_id?: string
          organization_id?: string
          organization_agent_id?: string
          retell_agent_id?: string
          call_status?: string
          start_timestamp?: string
          end_timestamp?: string
          duration_ms?: number
          transcript?: string | null
          dynamic_variables?: Json | null
          raw_data?: Json
          created_at?: string
        }
      }
    }
    Views: {
      call_analytics: {
        Row: {
          organization_id: string
          organization_name: string
          agent_id: string
          agent_name: string
          day: string
          total_calls: number
          total_minutes: number
          completed_calls: number
          incomplete_calls: number
        }
      }
    }
    Functions: {
      handle_call_webhook: {
        Args: { payload: Json }
        Returns: string
      }
    }
  }
}

export type CallPayload = Database['public']['Tables']['calls']['Row'] 