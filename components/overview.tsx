"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

const data = [
  {
    name: "<PERSON>",
    total: 4,
  },
  {
    name: "Feb",
    total: 8,
  },
  {
    name: "<PERSON>",
    total: 12,
  },
  {
    name: "<PERSON>",
    total: 6,
  },
  {
    name: "May",
    total: 15,
  },
  {
    name: "<PERSON>",
    total: 10,
  },
]

export function Overview() {

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data}>
        <XAxis
          dataKey="name"
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}`}
        />
        <Bar
          dataKey="total"
          fill="currentColor"
          radius={[4, 4, 0, 0]}
          className="fill-primary"
        />
      </BarChart>
    </ResponsiveContainer>
  )
}