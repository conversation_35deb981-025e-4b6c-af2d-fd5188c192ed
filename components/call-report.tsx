"use client"

import { Document, Page, Text, View, StyleSheet, Link, Image } from '@react-pdf/renderer'
import { Call } from '@/types/calls'

// Create styles using standard PDF fonts
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 35,
    fontFamily: 'Times-Roman'
  },
  headerBox: {
    backgroundColor: '#f8fafc',
    padding: 15,
    marginBottom: 20,
    borderRadius: 4
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: 10
  },
  headerLogo: {
    width: 120,
    height: 40,
    objectFit: 'contain',
    marginRight: 15
  },
  headerText: {
    flex: 1
  },
  header: {
    fontSize: 24,
    marginBottom: 6,
    color: '#1a1a1a',
    fontFamily: 'Times-Bold',
    textAlign: 'center'
  },
  subHeader: {
    fontSize: 11,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 3
  },
  section: {
    marginBottom: 15,
    borderBottom: '1pt solid #e2e8f0',
    paddingBottom: 15
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Times-Bold',
    marginBottom: 10,
    color: '#2563eb',
    backgroundColor: '#f1f5f9',
    padding: 6,
    borderRadius: 4
  },
  row: {
    flexDirection: 'row',
    marginBottom: 6
  },
  label: {
    width: 150,
    fontSize: 10,
    color: '#475569',
    fontFamily: 'Times-Bold'
  },
  value: {
    flex: 1,
    fontSize: 10,
    color: '#1a1a1a',
    fontFamily: 'Times-Roman'
  },
  transcript: {
    fontSize: 10,
    lineHeight: 1.5,
    color: '#1a1a1a',
    marginTop: 8,
    fontFamily: 'Times-Roman',
    paddingLeft: 8,
    borderLeft: '1pt solid #e2e8f0'
  },
  highlight: {
    backgroundColor: '#f0f9ff',
    padding: 8,
    borderRadius: 4,
    marginBottom: 8
  },
  footer: {
    position: 'absolute',
    bottom: 20,
    left: 35,
    right: 35,
    fontSize: 8,
    color: '#94a3b8',
    borderTop: '1pt solid #e2e8f0',
    paddingTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  footerLogo: {
    width: 24,
    height: 24,
    objectFit: 'contain',
    marginRight: 10
  },
  footerText: {
    flex: 1,
    textAlign: 'center',
    paddingRight: 34
  },
  transcriptAgent: {
    fontSize: 10,
    lineHeight: 1.5,
    color: '#2563eb',
    marginTop: 4,
    fontFamily: 'Times-Roman',
    paddingLeft: 8,
    backgroundColor: '#f0f7ff'
  },
  transcriptCustomer: {
    fontSize: 10,
    lineHeight: 1.5,
    color: '#059669',
    marginTop: 4,
    fontFamily: 'Times-Roman',
    paddingLeft: 8,
    backgroundColor: '#f0fdf9'
  },
  bulletPoint: {
    paddingLeft: 20,
    marginTop: 2
  },
  speakerBlock: {
    marginBottom: 8,
    borderRadius: 4,
    padding: 6
  },
  speakerLabel: {
    fontFamily: 'Times-Bold',
    marginRight: 4
  },
  recordingLink: {
    color: '#2563eb',
    textDecoration: 'underline',
    fontSize: 10
  }
})

interface CallReportProps {
  call: Call
}

export function CallReport({ call }: CallReportProps) {
  const formatDate = (date: string) => new Date(date).toLocaleString()
  const formatDuration = (seconds: number) => 
    `${Math.floor(seconds / 60)}m ${seconds % 60}s`
  
  const calculateGenerationTime = (startTime: string, durationSeconds: number) => {
    const callStart = new Date(startTime)
    const callEnd = new Date(callStart.getTime() + (durationSeconds * 1000))
    const generationTime = new Date(callEnd.getTime() + (15 * 1000)) // Add 15 seconds
    return generationTime.toLocaleString()
  }
  
  const getCustomerPhone = (phone1: string, phone2: string) => {
    return phone1 === '+13192585090' ? phone2 : phone1
  }

  const formatTranscript = (transcript: string) => {
    let currentSpeaker: 'agent' | 'customer' | null = null;
    let currentBlock: Array<{ text: string; isBullet: boolean }> = [];
    const blocks: Array<{ speaker: 'agent' | 'customer'; lines: Array<{ text: string; isBullet: boolean }> }> = [];

    transcript.split('\n').forEach((line) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;

      // Check for new speaker
      if (line.toLowerCase().startsWith('agent:')) {
        if (currentSpeaker && currentBlock.length > 0) {
          blocks.push({ speaker: currentSpeaker, lines: [...currentBlock] });
          currentBlock = [];
        }
        currentSpeaker = 'agent';
        const content = line.split(':').slice(1).join(':').trim();
        if (content) currentBlock.push({ text: content, isBullet: false });
      } else if (line.toLowerCase().startsWith('user:') || line.toLowerCase().startsWith('customer:')) {
        if (currentSpeaker && currentBlock.length > 0) {
          blocks.push({ speaker: currentSpeaker, lines: [...currentBlock] });
          currentBlock = [];
        }
        currentSpeaker = 'customer';
        const content = line.split(':').slice(1).join(':').trim();
        if (content) currentBlock.push({ text: content, isBullet: false });
      } else if (currentSpeaker) {
        // Handle bullet points and continuation lines
        const isBullet = trimmedLine.startsWith('-') || trimmedLine.startsWith('•');
        const text = isBullet ? trimmedLine.substring(1).trim() : trimmedLine;
        currentBlock.push({ text, isBullet });
      }
    });

    // Add the last block
    if (currentSpeaker && currentBlock.length > 0) {
      blocks.push({ speaker: currentSpeaker, lines: currentBlock });
    }

    return blocks.map((block, blockIndex) => (
      <View 
        key={blockIndex} 
        style={[
          styles.speakerBlock,
          block.speaker === 'agent' ? styles.transcriptAgent : styles.transcriptCustomer
        ]}
      >
        <Text style={styles.speakerLabel}>
          {block.speaker === 'agent' ? 'Agent:' : 'Customer:'}
        </Text>
        {block.lines.map((line, lineIndex) => (
          <Text key={lineIndex} style={line.isBullet ? styles.bulletPoint : {}}>
            {line.isBullet ? '• ' : ''}{line.text}
          </Text>
        ))}
      </View>
    ));
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.headerBox}>
          <View style={styles.headerContent}>
            <Image 
              src={process.env.NODE_ENV === 'development' 
                ? 'http://localhost:3000/valabs_logo.png'
                : '/valabs_logo.png'}
              style={styles.headerLogo}
            />
            <View style={styles.headerText}>
              <Text style={styles.header}>Call Summary Report</Text>
              <Text style={styles.subHeader}>Generated on {calculateGenerationTime(call.start_timestamp, call.duration_seconds)}</Text>
              <Text style={styles.subHeader}>Call ID: {call.call_id}</Text>
            </View>
          </View>
        </View>

        {/* Basic Info Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Call Overview</Text>
          <View style={styles.highlight}>
            <View style={styles.row}>
              <Text style={styles.label}>Date & Time:</Text>
              <Text style={styles.value}>{formatDate(call.start_timestamp)}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Duration:</Text>
              <Text style={styles.value}>{formatDuration(call.duration_seconds)}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Cost:</Text>
              <Text style={styles.value}>${(call.cost / 100).toFixed(3)}</Text>
            </View>
            {(call.recording_url || call.audio_url) && (
              <View style={styles.row}>
                <Text style={styles.label}>Recording:</Text>
                <Link src={call.recording_url || call.audio_url} style={styles.recordingLink}>
                  Listen to Call Recording
                </Link>
              </View>
            )}
          </View>
        </View>

        {/* Participants Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Call Participants</Text>
          <View style={styles.highlight}>
            <View style={styles.row}>
              <Text style={styles.label}>Customer Name:</Text>
              <Text style={styles.value}>
                {call.dynamic_variables 
                  ? typeof call.dynamic_variables === 'object'
                    ? call.dynamic_variables.customer_name 
                    : typeof call.dynamic_variables === 'string'
                      ? JSON.parse(call.dynamic_variables).customer_name 
                      : call.customer_name
                  : call.customer_name}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Customer Phone:</Text>
              <Text style={styles.value}>
                {getCustomerPhone(call.customer_phone || '', call.agent_phone || '')}
              </Text>
            </View>
          </View>
          <View style={[styles.highlight, { marginTop: 10 }]}>
            <View style={styles.row}>
              <Text style={styles.label}>Agent Name:</Text>
              <Text style={styles.value}>{call.agent_name}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Agent Phone:</Text>
              <Text style={styles.value}>+13192585090</Text>
            </View>
          </View>
        </View>

        {/* Analysis Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Call Analysis</Text>
          <View style={styles.highlight}>
            <View style={styles.row}>
              <Text style={styles.label}>Call Outcome:</Text>
              <Text style={styles.value}>
                {call.call_successful ? '✓ Successful' : '✗ Unsuccessful'}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Customer Sentiment:</Text>
              <Text style={styles.value}>{call.user_sentiment}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Disconnection Reason:</Text>
              <Text style={styles.value}>{call.disconnection_reason || 'N/A'}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>End-to-End Latency:</Text>
              <Text style={styles.value}>
                {call.end_to_end_latency ? `${call.end_to_end_latency}ms` : 'N/A'}
              </Text>
            </View>
          </View>
        </View>

        {/* Summary Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Call Summary</Text>
          <Text style={styles.transcript}>{call.call_summary}</Text>
        </View>

        {/* Transcript Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Full Transcript</Text>
          {formatTranscript(call.transcript)}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Image 
            src={process.env.NODE_ENV === 'development' 
              ? 'http://localhost:3000/valabs_logo.png'
              : '/valabs_logo.png'}
            style={styles.footerLogo}
          />
          <Text style={styles.footerText}>
            Generated by Voice Analytics Dashboard • Page 1 of 1
          </Text>
        </View>
      </Page>
    </Document>
  )
} 