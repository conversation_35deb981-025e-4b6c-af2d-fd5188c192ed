"use client";

import React, { useEffect, useState } from 'react';
import { useAgentSync } from '@/hooks/useAgentSync';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  Database,
  ExternalLink,
  Eye,
  Play,
  Info
} from "lucide-react";

export function AgentSyncManager() {
  const {
    loading,
    error,
    syncStatus,
    lastSyncResult,
    getSyncStatus,
    previewSync,
    executeSync,
    isSyncNeeded,
    timeSinceLastSync,
    syncHealth,
    clearError
  } = useAgentSync();

  const [showPreview, setShowPreview] = useState(false);

  // Load sync status on mount
  useEffect(() => {
    getSyncStatus();
  }, [getSyncStatus]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading) {
        getSyncStatus();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [getSyncStatus, loading]);

  const handlePreview = async () => {
    try {
      await previewSync();
      setShowPreview(true);
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleSync = async () => {
    try {
      await executeSync();
      setShowPreview(false);
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const getSyncHealthColor = () => {
    switch (syncHealth) {
      case 'healthy': return 'bg-green-500';
      case 'needs_sync': return 'bg-yellow-500';
      case 'warning': return 'bg-orange-500';
      case 'stale': return 'bg-red-500';
      case 'never_synced': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  const getSyncHealthText = () => {
    switch (syncHealth) {
      case 'healthy': return 'Healthy';
      case 'needs_sync': return 'Sync Needed';
      case 'warning': return 'Warning';
      case 'stale': return 'Stale';
      case 'never_synced': return 'Never Synced';
      default: return 'Unknown';
    }
  };

  return (
    <div className="space-y-6">
      {/* Sync Status Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Agent Synchronization
              </CardTitle>
              <CardDescription>
                Keep your agents in sync with RetellAI
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getSyncHealthColor()}`} />
              <Badge variant="outline">{getSyncHealthText()}</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4 border-red-800 bg-red-900/20">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-red-400">
                {error}
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={clearError}
                  className="ml-2 h-auto p-1 text-red-400 hover:text-red-300"
                >
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {syncStatus && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {syncStatus.current_status.retell_agents}
                </div>
                <div className="text-sm text-gray-400">RetellAI Agents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {syncStatus.current_status.db_agents_active}
                </div>
                <div className="text-sm text-gray-400">Active in DB</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-400">
                  {syncStatus.current_status.db_agents_inactive}
                </div>
                <div className="text-sm text-gray-400">Inactive in DB</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">
                  {syncStatus.current_status.retell_agents - syncStatus.current_status.db_agents_active}
                </div>
                <div className="text-sm text-gray-400">Missing</div>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Clock className="h-4 w-4" />
              Last sync: {timeSinceLastSync || 'Never'}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={getSyncStatus}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreview}
                disabled={loading}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview Changes
              </Button>
              <Button
                onClick={handleSync}
                disabled={loading || !isSyncNeeded}
                size="sm"
              >
                <Play className="h-4 w-4 mr-2" />
                Sync Now
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Results */}
      {showPreview && lastSyncResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Sync Preview
              {lastSyncResult.dry_run && (
                <Badge variant="secondary">Dry Run</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Changes that will be made when you sync
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-xl font-bold text-green-400">
                  {lastSyncResult.summary.added}
                </div>
                <div className="text-sm text-gray-400">To Add</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-blue-400">
                  {lastSyncResult.summary.updated}
                </div>
                <div className="text-sm text-gray-400">To Update</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-yellow-400">
                  {lastSyncResult.summary.removed}
                </div>
                <div className="text-sm text-gray-400">To Remove</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-red-400">
                  {lastSyncResult.summary.errors}
                </div>
                <div className="text-sm text-gray-400">Errors</div>
              </div>
            </div>

            {lastSyncResult.dry_run && (
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowPreview(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSync}
                  disabled={loading}
                >
                  Execute Sync
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Last Sync Summary */}
      {syncStatus?.last_sync_summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Last Sync Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold">
                  {syncStatus.last_sync_summary.total_retell_agents}
                </div>
                <div className="text-sm text-gray-400">RetellAI</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-400">
                  {syncStatus.last_sync_summary.added}
                </div>
                <div className="text-sm text-gray-400">Added</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">
                  {syncStatus.last_sync_summary.updated}
                </div>
                <div className="text-sm text-gray-400">Updated</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-400">
                  {syncStatus.last_sync_summary.removed}
                </div>
                <div className="text-sm text-gray-400">Removed</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-400">
                  {syncStatus.last_sync_summary.errors}
                </div>
                <div className="text-sm text-gray-400">Errors</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            How Sync Works
          </CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-gray-400 space-y-2">
          <p>• <strong>Add:</strong> Agents in RetellAI but not in your database will be added</p>
          <p>• <strong>Update:</strong> Existing agents will have their metadata refreshed</p>
          <p>• <strong>Remove:</strong> Agents deleted from RetellAI will be marked as inactive</p>
          <p>• <strong>Automatic:</strong> Sync runs every hour to keep data fresh</p>
        </CardContent>
      </Card>
    </div>
  );
}
