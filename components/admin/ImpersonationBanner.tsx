"use client";

import React from 'react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  User<PERSON>heck, 
  X, 
  Shield, 
  AlertTriangle,
  Loader2
} from "lucide-react";
import { useImpersonation } from '@/contexts/ImpersonationContext';

export function ImpersonationBanner() {
  const { 
    isImpersonating, 
    impersonatedUser, 
    originalAdmin, 
    stopImpersonation, 
    loading 
  } = useImpersonation();

  if (!isImpersonating || !impersonatedUser || !originalAdmin) {
    return null;
  }

  return (
    <Alert className="border-orange-500 bg-orange-500/10 mb-4">
      <AlertTriangle className="h-4 w-4 text-orange-500" />
      <AlertDescription className="flex items-center justify-between w-full">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <UserCheck className="h-4 w-4 text-orange-500" />
            <span className="font-medium text-orange-500">
              Impersonating User
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm">
              Viewing as: <strong>{impersonatedUser.email}</strong>
            </span>
            <Badge variant="outline" className="text-xs">
              {impersonatedUser.role === 'admin' && <Shield className="h-3 w-3 mr-1" />}
              {impersonatedUser.role}
            </Badge>
          </div>

          <div className="text-xs text-muted-foreground">
            Original admin: {originalAdmin.email}
          </div>
        </div>

        <Button
          onClick={stopImpersonation}
          disabled={loading}
          variant="outline"
          size="sm"
          className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <X className="h-4 w-4 mr-2" />
          )}
          Stop Impersonating
        </Button>
      </AlertDescription>
    </Alert>
  );
}
