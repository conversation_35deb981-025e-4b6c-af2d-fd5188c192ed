import { Card } from "@/components/ui/card"
import { LucideIcon } from "lucide-react"
import { ArrowDownIcon, ArrowUpIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface MetricCardProps {
  title: string
  value: string | number
  change: number
  trend: "up" | "down"
  onClick?: () => void
  className?: string
  icon: LucideIcon
}

export default function MetricCard({ 
  title, 
  value, 
  change, 
  trend,
  onClick,
  className,
  icon: Icon
}: MetricCardProps) {
  const isPositive = trend === "up"
  const changeAbs = Math.abs(change)
  
  const displayValue = typeof value === 'number' ? value.toString() : value

  return (
    <Card 
      className={cn("p-6", className)} 
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-4" id={`metric-${title}`}>
            {title}
          </h3>
          <div 
            className="flex items-baseline justify-between"
            aria-labelledby={`metric-${title}`}
            role="region"
          >
            <p className="text-2xl font-semibold">{displayValue}</p>
            <div className={cn("flex items-center text-sm", isPositive ? "text-green-600" : "text-red-600")}>
              {isPositive ? <ArrowUpIcon className="h-4 w-4 mr-1" /> : <ArrowDownIcon className="h-4 w-4 mr-1" />}
              {changeAbs.toFixed(1)}%
            </div>
          </div>
        </div>
        <Icon className="h-6 w-6 text-muted-foreground" />
      </div>
    </Card>
  )
}

