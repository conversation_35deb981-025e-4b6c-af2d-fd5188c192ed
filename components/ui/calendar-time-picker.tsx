"use client"

import * as React from "react"
import { Calendar as CalendarI<PERSON>, Clock } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { format } from "date-fns"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface CalendarTimePickerProps {
  date?: Date
  onSelect?: (date: Date | undefined) => void
  className?: string
}

export function CalendarTimePicker({ date, onSelect, className }: CalendarTimePickerProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(date)
  const [hour, setHour] = React.useState(date?.getHours() || 0)
  const [minute, setMinute] = React.useState(date?.getMinutes() || 0)

  const minuteRef = React.useRef<HTMLDivElement>(null)
  const hourRef = React.useRef<HTMLDivElement>(null)

  // Update the date when hour or minute changes
  React.useEffect(() => {
    if (selectedDate) {
      const newDate = new Date(selectedDate)
      newDate.setHours(hour)
      newDate.setMinutes(minute)
      onSelect?.(newDate)
    }
  }, [hour, minute, selectedDate, onSelect])

  // Scroll to current time on mount
  React.useEffect(() => {
    if (hourRef.current && minuteRef.current) {
      hourRef.current.scrollTop = hour * 40
      minuteRef.current.scrollTop = Math.floor(minute / 5) * 40
    }
  }, [])

  const hours = Array.from({ length: 24 }, (_, i) => i)
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5)

  const handleSelect = (day: Date | undefined) => {
    if (day) {
      const newDate = new Date(day)
      newDate.setHours(hour)
      newDate.setMinutes(minute)
      setSelectedDate(newDate)
      onSelect?.(newDate)
    }
  }

  return (
    <div className={className}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-[280px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "PPP HH:mm") : <span>Pick a date & time</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Tabs defaultValue="calendar">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="calendar">
                <CalendarIcon className="mr-2 h-4 w-4" />
                Date
              </TabsTrigger>
              <TabsTrigger value="time">
                <Clock className="mr-2 h-4 w-4" />
                Time
              </TabsTrigger>
            </TabsList>
            <TabsContent value="calendar" className="p-0">
              <DayPicker
                mode="single"
                selected={selectedDate}
                onSelect={handleSelect}
                initialFocus
                className="p-3"
              />
            </TabsContent>
            <TabsContent value="time" className="p-4">
              <div className="flex gap-4 justify-center">
                <div className="flex flex-col items-center">
                  <div className="text-sm font-medium mb-1">Hour</div>
                  <ScrollArea
                    ref={hourRef}
                    className="h-[200px] w-[60px] rounded-md border"
                  >
                    <div className="flex flex-col items-center py-2">
                      {hours.map((h) => (
                        <div
                          key={h}
                          className={cn(
                            "h-10 w-full flex items-center justify-center cursor-pointer hover:bg-accent rounded-md",
                            hour === h && "bg-primary text-primary-foreground hover:bg-primary"
                          )}
                          onClick={() => setHour(h)}
                        >
                          {h.toString().padStart(2, "0")}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-sm font-medium mb-1">Minute</div>
                  <ScrollArea
                    ref={minuteRef}
                    className="h-[200px] w-[60px] rounded-md border"
                  >
                    <div className="flex flex-col items-center py-2">
                      {minutes.map((m) => (
                        <div
                          key={m}
                          className={cn(
                            "h-10 w-full flex items-center justify-center cursor-pointer hover:bg-accent rounded-md",
                            minute === m && "bg-primary text-primary-foreground hover:bg-primary"
                          )}
                          onClick={() => setMinute(m)}
                        >
                          {m.toString().padStart(2, "0")}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </PopoverContent>
      </Popover>
    </div>
  )
} 