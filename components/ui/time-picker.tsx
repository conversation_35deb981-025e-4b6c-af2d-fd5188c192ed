"use client"

import * as React from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

interface TimePickerProps {
  date: Date
  onChange: (date: Date) => void
  className?: string
}

export function TimePicker({ date, onChange, className }: TimePickerProps) {
  const minuteRef = React.useRef<HTMLDivElement>(null)
  const hourRef = React.useRef<HTMLDivElement>(null)

  const [hour, setHour] = React.useState(date.getHours())
  const [minute, setMinute] = React.useState(date.getMinutes())

  // Update the date when hour or minute changes
  React.useEffect(() => {
    const newDate = new Date(date)
    newDate.setHours(hour)
    newDate.setMinutes(minute)
    onChange(newDate)
  }, [hour, minute, date, onChange])

  // Scroll to current time on mount
  React.useEffect(() => {
    if (hourRef.current && minuteRef.current) {
      hourRef.current.scrollTop = hour * 40
      minuteRef.current.scrollTop = Math.floor(minute / 5) * 40
    }
  }, [])

  const hours = Array.from({ length: 24 }, (_, i) => i)
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5)

  return (
    <div className={cn("flex gap-2", className)}>
      <div className="flex flex-col items-center">
        <div className="text-sm font-medium mb-1">Hour</div>
        <ScrollArea
          ref={hourRef}
          className="h-[160px] w-[60px] rounded-md border"
        >
          <div className="flex flex-col items-center py-2">
            {hours.map((h) => (
              <div
                key={h}
                className={cn(
                  "h-10 w-full flex items-center justify-center cursor-pointer hover:bg-accent rounded-md",
                  hour === h && "bg-primary text-primary-foreground hover:bg-primary"
                )}
                onClick={() => setHour(h)}
              >
                {h.toString().padStart(2, "0")}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
      <div className="flex flex-col items-center">
        <div className="text-sm font-medium mb-1">Minute</div>
        <ScrollArea
          ref={minuteRef}
          className="h-[160px] w-[60px] rounded-md border"
        >
          <div className="flex flex-col items-center py-2">
            {minutes.map((m) => (
              <div
                key={m}
                className={cn(
                  "h-10 w-full flex items-center justify-center cursor-pointer hover:bg-accent rounded-md",
                  minute === m && "bg-primary text-primary-foreground hover:bg-primary"
                )}
                onClick={() => setMinute(m)}
              >
                {m.toString().padStart(2, "0")}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  )
} 