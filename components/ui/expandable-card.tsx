"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { Maximize2, Minimize2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface ExpandableCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  description?: string
  children: React.ReactNode
}

export function ExpandableCard({ title, description, children, className, ...props }: ExpandableCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <>
      <Card className={cn("p-6 relative", className)} {...props}>
        <button
          onClick={() => setIsExpanded(true)}
          className="absolute top-4 right-4 p-1.5 hover:bg-muted rounded-md text-muted-foreground"
        >
          <Maximize2 className="h-4 w-4" />
        </button>
        <h3 className="text-sm font-medium mb-2">{title}</h3>
        {description && (
          <p className="text-xs text-muted-foreground mb-4">{description}</p>
        )}
        <div className="h-[200px]">
          {children}
        </div>
      </Card>

      {isExpanded && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50">
          <div className="fixed inset-4 bg-background border rounded-lg shadow-lg p-6 overflow-auto">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-lg font-semibold">{title}</h2>
                {description && (
                  <p className="text-sm text-muted-foreground">{description}</p>
                )}
              </div>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1.5 hover:bg-muted rounded-md text-muted-foreground"
              >
                <Minimize2 className="h-4 w-4" />
              </button>
            </div>
            <div className="h-[calc(100vh-12rem)]">
              {children}
            </div>
          </div>
        </div>
      )}
    </>
  )
} 