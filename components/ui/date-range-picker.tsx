"use client"

import * as React from "react"
import { addDays, format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { CalendarTimePicker } from "@/components/ui/calendar-time-picker"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function DateRangePicker({
  className,
  onChange,
}: {
  className?: string
  onChange?: (date: DateRange | undefined) => void
}) {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: new Date(),
    to: addDays(new Date(), 7),
  })

  const handleStartDateSelect = (newDate: Date | undefined) => {
    if (newDate && date) {
      const newRange: DateRange = {
        from: newDate,
        to: date.to,
      }
      setDate(newRange)
      onChange?.(newRange)
    }
  }

  const handleEndDateSelect = (newDate: Date | undefined) => {
    if (newDate && date) {
      const newRange: DateRange = {
        from: date.from,
        to: newDate,
      }
      setDate(newRange)
      onChange?.(newRange)
    }
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-[300px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "PPP HH:mm")} -{" "}
                  {format(date.to, "PPP HH:mm")}
                </>
              ) : (
                format(date.from, "PPP HH:mm")
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Tabs defaultValue="start">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="start">Start Date & Time</TabsTrigger>
              <TabsTrigger value="end">End Date & Time</TabsTrigger>
            </TabsList>
            <TabsContent value="start" className="p-0">
              <CalendarTimePicker
                date={date?.from}
                onSelect={handleStartDateSelect}
              />
            </TabsContent>
            <TabsContent value="end" className="p-0">
              <CalendarTimePicker
                date={date?.to}
                onSelect={handleEndDateSelect}
              />
            </TabsContent>
          </Tabs>
        </PopoverContent>
      </Popover>
    </div>
  )
} 