"use client"

import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useEffect, useState } from "react"
import { supabase } from "@/lib/supabase"

interface RecentCall {
  call_id: string
  customer_name: string
  start_timestamp: string
  duration_seconds: number
  call_successful: boolean
}

export function RecentCalls() {
  const [recentCalls, setRecentCalls] = useState<RecentCall[]>([])

  useEffect(() => {
    const fetchRecentCalls = async () => {
      const { data, error } = await supabase
        .from('call_analytics')
        .select('call_id, customer_name, start_timestamp, duration_seconds, call_successful')
        .order('start_timestamp', { ascending: false })
        .limit(5)

      if (error) {
        console.error('Error fetching recent calls:', error)
        return
      }

      setRecentCalls(data || [])
    }

    fetchRecentCalls()
  }, [])

  return (
    <div className="space-y-8">
      {recentCalls.map((call) => (
        <div key={call.call_id} className="flex items-center">
          <Avatar className="h-9 w-9">
            <AvatarFallback>
              {call.customer_name.split(" ").map((n) => n[0]).join("")}
            </AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{call.customer_name}</p>
            <p className="text-sm text-muted-foreground">
              {new Date(call.start_timestamp).toLocaleString()} • {`${Math.floor(call.duration_seconds / 60)}m ${call.duration_seconds % 60}s`}
            </p>
          </div>
          <div className="ml-auto font-medium">
            <span
              className={
                call.call_successful
                  ? "text-green-500"
                  : "text-red-500"
              }
            >
              {call.call_successful ? "Successful" : "Failed"}
            </span>
          </div>
        </div>
      ))}
    </div>
  )
} 