"use client"

import {
  <PERSON>,
  <PERSON>Chart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  Bar,
  BarChart,
} from "recharts"

interface LineChartProps {
  data: Array<{
    date: string
    latest: number
    previous: number
  }>
  colors: [string, string]
  chartType?: "line" | "area" | "bar"
}

export function LineChart({ data, colors, chartType = "line" }: LineChartProps) {
  const commonProps = {
    data,
    margin: { top: 5, right: 5, bottom: 5, left: 5 }
  }

  const commonChildren = (
    <>
      <XAxis 
        dataKey="date" 
        fontSize={12}
        tickLine={false}
        axisLine={false}
      />
      <YAxis 
        fontSize={12}
        tickLine={false}
        axisLine={false}
      />
      <Tooltip 
        contentStyle={{ 
          backgroundColor: 'var(--background)',
          border: '1px solid var(--border)'
        }}
      />
    </>
  )

  if (chartType === "area") {
    return (
      <div className="h-[200px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart {...commonProps}>
            {commonChildren}
            <Area
              type="monotone"
              dataKey="latest"
              stroke={colors[0]}
              fill={colors[0]}
              fillOpacity={0.3}
              strokeWidth={2}
            />
            <Area
              type="monotone"
              dataKey="previous"
              stroke={colors[1]}
              fill={colors[1]}
              fillOpacity={0.3}
              strokeWidth={2}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    )
  }

  if (chartType === "bar") {
    return (
      <div className="h-[200px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart {...commonProps}>
            {commonChildren}
            <Bar dataKey="latest" fill={colors[0]} />
            <Bar dataKey="previous" fill={colors[1]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    )
  }

  return (
    <div className="h-[200px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart {...commonProps}>
          {commonChildren}
          <Line 
            type="monotone" 
            dataKey="latest" 
            stroke={colors[0]} 
            strokeWidth={2}
            dot={false}
          />
          <Line 
            type="monotone" 
            dataKey="previous" 
            stroke={colors[1]} 
            strokeWidth={2}
            dot={false}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  )
}

