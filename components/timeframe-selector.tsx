"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useTimeframe } from "@/contexts/TimeframeContext"

interface TimeframeSelectorProps {
  className?: string
  showToday?: boolean
}

export function TimeframeSelector({ className = "w-[180px]", showToday = true }: TimeframeSelectorProps) {
  const { timeframe, setTimeframe, getTimeframeLabel } = useTimeframe()

  return (
    <Select value={timeframe} onValueChange={setTimeframe}>
      <SelectTrigger className={className}>
        <SelectValue>{getTimeframeLabel(timeframe)}</SelectValue>
      </SelectTrigger>
      <SelectContent>
        {showToday && <SelectItem value="today">Today</SelectItem>}
        <SelectItem value="7">Last 7 days</SelectItem>
        <SelectItem value="14">Last 14 days</SelectItem>
        <SelectItem value="30">Last 30 days</SelectItem>
        <SelectItem value="90">Last 90 days</SelectItem>
        <SelectItem value="all">All Time</SelectItem>
      </SelectContent>
    </Select>
  )
}
