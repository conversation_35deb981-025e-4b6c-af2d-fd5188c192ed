"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  { name: 'Fertility Insurance', value: 25, color: '#3b82f6' },
  { name: 'Pet Insurance', value: 20, color: '#ef4444' },
  { name: 'Critical Illness', value: 15, color: '#22c55e' },
  { name: 'Accident Insurance', value: 12, color: '#eab308' },
  { name: 'Legal Services', value: 10, color: '#8b5cf6' },
  { name: 'Other', value: 18, color: '#64748b' },
]

export function TopicsDistribution() {
  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>Popular Topics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieC<PERSON>>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
} 