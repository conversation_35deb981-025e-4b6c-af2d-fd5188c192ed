"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, CartesianGrid } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  { date: '01/24', appointments: 12 },
  { date: '01/25', appointments: 15 },
  { date: '01/26', appointments: 8 },
  { date: '01/27', appointments: 21 },
  { date: '01/28', appointments: 14 },
  { date: '01/29', appointments: 18 },
  { date: '01/30', appointments: 16 },
]

export function AppointmentsBooked() {
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Appointments Booked</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="appointments" fill="#8b5cf6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
} 