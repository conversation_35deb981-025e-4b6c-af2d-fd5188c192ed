"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, ResponsiveContainer, CartesianGrid } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  { date: '01/24', calls: 45 },
  { date: '01/25', calls: 52 },
  { date: '01/26', calls: 49 },
  { date: '01/27', calls: 63 },
  { date: '01/28', calls: 38 },
  { date: '01/29', calls: 71 },
  { date: '01/30', calls: 56 },
]

export function CallsTimeline() {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Calls Over Time</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="calls" 
                stroke="#2563eb" 
                strokeWidth={2}
                dot={{ fill: '#2563eb' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
} 