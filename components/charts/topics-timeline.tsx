"use client"

import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  {
    date: '01/24',
    'Fertility Insurance': 15,
    'Pet Insurance': 12,
    'Critical Illness': 8,
    'Accident Insurance': 6,
  },
  {
    date: '01/25',
    'Fertility Insurance': 18,
    'Pet Insurance': 14,
    'Critical Illness': 10,
    'Accident Insurance': 8,
  },
  {
    date: '01/26',
    'Fertility Insurance': 12,
    'Pet Insurance': 18,
    'Critical Illness': 14,
    'Accident Insurance': 10,
  },
  {
    date: '01/27',
    'Fertility Insurance': 22,
    'Pet Insurance': 16,
    'Critical Illness': 12,
    'Accident Insurance': 7,
  },
  {
    date: '01/28',
    'Fertility Insurance': 16,
    'Pet Insurance': 20,
    'Critical Illness': 15,
    'Accident Insurance': 12,
  },
  {
    date: '01/29',
    'Fertility Insurance': 25,
    'Pet Insurance': 18,
    'Critical Illness': 13,
    'Accident Insurance': 9,
  },
  {
    date: '01/30',
    'Fertility Insurance': 20,
    'Pet Insurance': 15,
    'Critical Illness': 11,
    'Accident Insurance': 8,
  }
]

export function TopicsTimeline() {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Topics Over Time</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="Fertility Insurance" stackId="a" fill="#3b82f6" />
              <Bar dataKey="Pet Insurance" stackId="a" fill="#ef4444" />
              <Bar dataKey="Critical Illness" stackId="a" fill="#22c55e" />
              <Bar dataKey="Accident Insurance" stackId="a" fill="#eab308" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
} 