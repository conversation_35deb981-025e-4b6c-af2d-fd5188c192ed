"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Database,
  Eye,
  Play
} from "lucide-react";
import { useAgentSync } from '@/hooks/useAgentSync';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface AgentSyncButtonProps {
  onSyncComplete?: () => void;
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  showStatus?: boolean;
}

export function AgentSyncButton({
  onSyncComplete,
  variant = "outline",
  size = "sm",
  showStatus = true
}: AgentSyncButtonProps) {
  const { toast } = useToast();
  const [showPreview, setShowPreview] = useState(false);

  const {
    loading,
    error,
    syncStatus,
    lastSyncResult,
    executeSync,
    previewSync,
    isSyncNeeded,
    syncHealth,
    clearError,
    clearLastResult
  } = useAgentSync();

  const handleSync = async () => {
    try {
      clearError();
      const result = await executeSync();

      if (result.success) {
        toast({
          title: "Sync Completed",
          description: `Successfully synced ${result.summary.added} new agents, updated ${result.summary.updated} agents.`,
          variant: "default",
        });

        onSyncComplete?.();
      } else {
        throw new Error("Sync failed");
      }
    } catch (error) {
      console.error('Sync error:', error);
      toast({
        title: "Sync Failed",
        description: error instanceof Error ? error.message : "Failed to sync with RetellAI. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePreview = async () => {
    try {
      // Clear old result first
      clearLastResult();
      // Fetch fresh preview data
      const result = await previewSync();
      console.log('Preview sync result:', result);
      setShowPreview(true);
    } catch (error) {
      console.error('Preview error:', error);
    }
  };

  const getSyncHealthColor = () => {
    switch (syncHealth) {
      case 'healthy': return 'bg-green-500';
      case 'needs_sync': return 'bg-yellow-500';
      case 'warning': return 'bg-red-500';
      case 'stale': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getSyncHealthText = () => {
    switch (syncHealth) {
      case 'healthy': return 'In Sync';
      case 'needs_sync': return 'Sync Needed';
      case 'warning': return 'Warning';
      case 'stale': return 'Stale';
      default: return 'Unknown';
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Status Indicator */}
      {showStatus && syncStatus && (
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${getSyncHealthColor()}`} />
          <Badge variant={syncHealth === 'healthy' ? 'default' : 'secondary'} className="text-xs">
            {syncStatus.current_status.retell_agents} RetellAI | {syncStatus.current_status.db_agents_active} Local
          </Badge>
        </div>
      )}

      {/* Preview Button */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogTrigger asChild>
          <Button
            onClick={handlePreview}
            disabled={loading}
            variant="ghost"
            size={size}
            className="flex items-center gap-1"
          >
            <Eye className="h-3 w-3" />
            Preview
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Sync Preview</DialogTitle>
            <DialogDescription>
              Review what changes will be made when syncing with RetellAI
            </DialogDescription>
          </DialogHeader>

          {lastSyncResult && (
            <div className="space-y-4">
              {console.log('Dialog showing lastSyncResult:', lastSyncResult)}
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold text-green-400">{lastSyncResult.summary.added}</div>
                  <div className="text-sm text-gray-400">To Add</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-blue-400">{lastSyncResult.summary.updated}</div>
                  <div className="text-sm text-gray-400">To Update</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-red-400">{lastSyncResult.summary.removed}</div>
                  <div className="text-sm text-gray-400">To Remove</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-yellow-400">{lastSyncResult.summary.errors}</div>
                  <div className="text-sm text-gray-400">Errors</div>
                </div>
              </div>

              {lastSyncResult.details.added.length > 0 && (
                <div>
                  <h4 className="font-medium text-green-400 mb-2">Agents to Add:</h4>
                  <ul className="text-sm space-y-1">
                    {lastSyncResult.details.added.map((agent, index) => (
                      <li key={index} className="text-gray-300">• {agent.name}</li>
                    ))}
                  </ul>
                </div>
              )}

              {lastSyncResult.details.updated.length > 0 && (
                <div>
                  <h4 className="font-medium text-blue-400 mb-2">Agents to Update:</h4>
                  <ul className="text-sm space-y-1">
                    {lastSyncResult.details.updated.map((agent, index) => (
                      <li key={index} className="text-gray-300">• {agent.agent_id}</li>
                    ))}
                  </ul>
                </div>
              )}

              {lastSyncResult.details.errors.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-400 mb-2">Errors:</h4>
                  <ul className="text-sm space-y-1">
                    {lastSyncResult.details.errors.map((error, index) => (
                      <li key={index} className="text-red-300">• {error.agent_id}: {error.error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Sync Button */}
      <Button
        onClick={handleSync}
        disabled={loading}
        variant={variant}
        size={size}
        className="flex items-center gap-2"
      >
        <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        {loading ? 'Syncing...' : 'Sync with RetellAI'}
      </Button>
    </div>
  );
}
