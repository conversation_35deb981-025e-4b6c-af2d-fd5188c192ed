# Agent Sync Components

This directory contains components for synchronizing agents between your local database and RetellAI.

## Components

### AgentSyncButton
A button component that provides sync functionality with preview capabilities.

**Props:**
- `onSyncComplete?: () => void` - Callback when sync completes successfully
- `variant?: "default" | "outline" | "ghost"` - Button variant
- `size?: "default" | "sm" | "lg"` - Button size
- `showStatus?: boolean` - Whether to show sync status indicator

**Features:**
- Shows current sync status (RetellAI vs Local agent counts)
- Preview changes before applying them
- Visual sync health indicator
- Loading states during sync operations

### AgentSyncStatus
A comprehensive status dashboard for agent synchronization.

**Props:**
- `onSyncComplete?: () => void` - Callback when sync completes successfully
- `autoRefresh?: boolean` - Whether to auto-refresh status (default: true)
- `refreshInterval?: number` - Refresh interval in ms (default: 30000)

**Features:**
- Real-time sync status monitoring
- Detailed sync statistics
- Last sync summary with breakdown
- Auto-refresh capabilities
- Sync health indicators

### AgentSyncNotification
A notification component that alerts users when sync is needed or has errors.

**Props:**
- `onSyncClick?: () => void` - Callback when sync button is clicked
- `dismissible?: boolean` - Whether notification can be dismissed (default: true)
- `autoHide?: boolean` - Whether to auto-hide notification (default: false)
- `autoHideDelay?: number` - Auto-hide delay in ms (default: 5000)

**Features:**
- Shows sync needed alerts
- Displays sync errors
- Success notifications when in sync
- Dismissible notifications
- Auto-hide functionality

## Usage Examples

### Basic Sync Button
```tsx
import { AgentSyncButton } from '@/components/agent-sync';

function MyComponent() {
  const handleSyncComplete = () => {
    // Refresh your data
    fetchAgents();
  };

  return (
    <AgentSyncButton 
      onSyncComplete={handleSyncComplete}
      variant="outline"
      showStatus={true}
    />
  );
}
```

### Full Sync Dashboard
```tsx
import { AgentSyncStatus } from '@/components/agent-sync';

function AdminSyncPage() {
  return (
    <div>
      <h1>Agent Synchronization</h1>
      <AgentSyncStatus />
    </div>
  );
}
```

### Sync Notifications
```tsx
import { AgentSyncNotification } from '@/components/agent-sync';

function AgentBuilderPage() {
  const handleSync = async () => {
    // Perform sync
  };

  return (
    <div>
      <AgentSyncNotification 
        onSyncClick={handleSync}
        dismissible={true}
      />
      {/* Rest of your page */}
    </div>
  );
}
```

## Sync Process

The sync process works as follows:

1. **Fetch** - Retrieves agents from both RetellAI and local database
2. **Compare** - Identifies differences between the two sources
3. **Preview** - Shows what changes will be made (optional)
4. **Execute** - Applies the changes:
   - **Add**: Agents in RetellAI but not in local DB are added
   - **Update**: Existing agents have their metadata refreshed
   - **Remove**: Agents deleted from RetellAI are marked as inactive
5. **Report** - Provides summary of changes made

## Sync Health States

- **Healthy**: Agents are synchronized
- **Needs Sync**: Differences detected between RetellAI and local DB
- **Warning**: Last sync completed with errors
- **Stale**: Last sync was more than 24 hours ago
- **Never Synced**: No synchronization has been performed

## API Endpoints

The components use these API endpoints:

- `GET /api/sync/agents?organization_id={id}` - Get sync status
- `POST /api/sync/agents` - Execute sync (with dry_run option)

## Hooks

Uses the `useAgentSync` hook which provides:
- Sync status monitoring
- Sync execution
- Preview functionality
- Error handling
- Health status calculation
