"use client";

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Upload,
  FileText,
  Trash2,
  Download,
  ChevronLeft,
  ChevronRight,
  Plus,
  AlertCircle,
  CheckCircle,
  Loader2,
  Globe,
  Database
} from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";
import { useToast } from "@/components/ui/use-toast";
// import { useDropzone } from 'react-dropzone';

interface KnowledgeBaseStepProps {
  selectedTemplate: AgentTemplate | null;
  onNext: () => void;
  onBack: () => void;
}

interface Document {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  progress?: number;
  errorMessage?: string;
}

interface PrebuiltKnowledgeBase {
  id: string;
  name: string;
  description: string;
  documentCount: number;
  category: string;
  isRecommended?: boolean;
  topics: string[];
}

const PREBUILT_KNOWLEDGE_BASES: PrebuiltKnowledgeBase[] = [
  {
    id: 'insurance-basics',
    name: 'Insurance Fundamentals',
    description: 'Basic insurance concepts, terminology, and common questions',
    documentCount: 25,
    category: 'General',
    isRecommended: true,
    topics: ['Policy basics', 'Coverage types', 'Deductibles', 'Premiums', 'Claims process']
  },
  {
    id: 'aflac-products',
    name: 'Aflac Product Portfolio',
    description: 'Comprehensive information about Aflac insurance products and benefits',
    documentCount: 45,
    category: 'Products',
    isRecommended: true,
    topics: ['Accident insurance', 'Hospital indemnity', 'Critical illness', 'Dental', 'Vision', 'Life insurance']
  },
  {
    id: 'enrollment-guide',
    name: 'Benefits Enrollment Guide',
    description: 'Step-by-step enrollment processes and requirements',
    documentCount: 18,
    category: 'Enrollment',
    topics: ['Enrollment periods', 'Eligibility', 'Plan selection', 'Life events', 'Documentation']
  },
  {
    id: 'claims-procedures',
    name: 'Claims Processing Procedures',
    description: 'Detailed claims filing and processing information',
    documentCount: 32,
    category: 'Claims',
    topics: ['Filing claims', 'Required documents', 'Processing times', 'Appeals', 'Reimbursement']
  }
];

export default function KnowledgeBaseStep({
  selectedTemplate,
  onNext,
  onBack
}: KnowledgeBaseStepProps) {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('prebuilt');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newDocuments: Document[] = acceptedFiles.map(file => ({
      id: crypto.randomUUID(),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0
    }));

    setDocuments(prev => [...prev, ...newDocuments]);

    // Simulate upload process
    newDocuments.forEach(doc => {
      simulateUpload(doc.id);
    });
  }, []);

  // Temporarily disable dropzone until we install the dependency
  const getRootProps = () => ({});
  const getInputProps = () => ({});
  const isDragActive = false;

  const simulateUpload = (docId: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 20;

      setDocuments(prev => prev.map(doc =>
        doc.id === docId
          ? { ...doc, progress: Math.min(progress, 100) }
          : doc
      ));

      if (progress >= 100) {
        clearInterval(interval);
        setDocuments(prev => prev.map(doc =>
          doc.id === docId
            ? { ...doc, status: 'processing', progress: 100 }
            : doc
        ));

        // Simulate processing
        setTimeout(() => {
          setDocuments(prev => prev.map(doc =>
            doc.id === docId
              ? { ...doc, status: 'ready' }
              : doc
          ));
        }, 2000);
      }
    }, 500);
  };

  const removeDocument = (docId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== docId));
  };

  const toggleKnowledgeBase = (kbId: string) => {
    setSelectedKnowledgeBases(prev =>
      prev.includes(kbId)
        ? prev.filter(id => id !== kbId)
        : [...prev, kbId]
    );
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Byte';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)).toString());
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const hasContent = documents.length > 0 || selectedKnowledgeBases.length > 0;

  return (
    <div className="relative z-10 max-w-4xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 4: Knowledge Base</h3>
        <p className="text-gray-300 mb-6">
          Add documents and information sources to make your agent more knowledgeable and helpful.
        </p>

        {selectedTemplate && (
          <Card className="bg-blue-900/20 border-blue-800 mb-6">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Recommended Knowledge for {selectedTemplate.name}
              </CardTitle>
              <CardDescription className="text-gray-300">
                Based on your template, we recommend these knowledge base topics:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {selectedTemplate.knowledgeBaseTopics.map((topic) => (
                  <Badge key={topic} variant="secondary" className="bg-blue-100 text-blue-800">
                    {topic}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="bg-gray-800 mb-6">
            <TabsTrigger value="prebuilt" className="data-[state=active]:bg-gray-700">
              <Database className="h-4 w-4 mr-2" />
              Pre-built Knowledge
            </TabsTrigger>
            <TabsTrigger value="upload" className="data-[state=active]:bg-gray-700">
              <Upload className="h-4 w-4 mr-2" />
              Upload Documents
            </TabsTrigger>
            <TabsTrigger value="web" className="data-[state=active]:bg-gray-700">
              <Globe className="h-4 w-4 mr-2" />
              Web Sources
            </TabsTrigger>
          </TabsList>

          <TabsContent value="prebuilt" className="space-y-4">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Pre-built Knowledge Bases</CardTitle>
                <CardDescription className="text-gray-300">
                  Select from our curated knowledge bases to quickly add expertise to your agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {PREBUILT_KNOWLEDGE_BASES.map((kb) => (
                    <Card
                      key={kb.id}
                      className={`cursor-pointer transition-all duration-200 ${
                        selectedKnowledgeBases.includes(kb.id)
                          ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'bg-gray-800 border-gray-700 hover:bg-gray-750'
                      }`}
                      onClick={() => toggleKnowledgeBase(kb.id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-white text-base">{kb.name}</CardTitle>
                          {kb.isRecommended && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                              Recommended
                            </Badge>
                          )}
                        </div>
                        <CardDescription className="text-gray-300 text-sm">
                          {kb.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-sm text-gray-400 mb-3">
                          <span>{kb.documentCount} documents</span>
                          <Badge variant="outline" className="border-gray-600 text-gray-400">
                            {kb.category}
                          </Badge>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {kb.topics.slice(0, 3).map((topic) => (
                            <Badge key={topic} variant="outline" className="text-xs border-gray-600 text-gray-400">
                              {topic}
                            </Badge>
                          ))}
                          {kb.topics.length > 3 && (
                            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                              +{kb.topics.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Upload Documents</CardTitle>
                <CardDescription className="text-gray-300">
                  Upload your own documents to create custom knowledge for your agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive
                      ? 'border-blue-500 bg-blue-900/20'
                      : 'border-gray-700 hover:border-gray-600'
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-white mb-2">
                    {isDragActive ? 'Drop files here...' : 'Drag & drop files here, or click to select'}
                  </div>
                  <div className="text-sm text-gray-400">
                    Supports PDF, DOC, DOCX, TXT, MD files up to 10MB each
                  </div>
                </div>

                {documents.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-white font-medium mb-3">Uploaded Documents</h4>
                    <div className="space-y-2">
                      {documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-800 rounded-md">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(doc.status)}
                            <div>
                              <div className="text-white text-sm">{doc.name}</div>
                              <div className="text-gray-400 text-xs">
                                {formatFileSize(doc.size)} • {doc.status}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {doc.status === 'uploading' && doc.progress !== undefined && (
                              <div className="w-20 bg-gray-700 rounded-full h-1">
                                <div
                                  className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                                  style={{ width: `${doc.progress}%` }}
                                />
                              </div>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDocument(doc.id)}
                              className="text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="web" className="space-y-4">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Web Sources</CardTitle>
                <CardDescription className="text-gray-300">
                  Connect web sources like websites, APIs, or databases (Coming Soon)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-white mb-2">Web Source Integration</div>
                  <div className="text-sm text-gray-400 mb-4">
                    This feature will allow you to connect websites, APIs, and live data sources
                  </div>
                  <Button variant="outline" disabled className="border-gray-700 text-gray-400">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Web Source (Coming Soon)
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Summary */}
        {hasContent && (
          <Card className="bg-gray-900 border-gray-800 mt-6">
            <CardHeader>
              <CardTitle className="text-white">Knowledge Base Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-400">Pre-built Knowledge Bases</div>
                  <div className="text-white">{selectedKnowledgeBases.length} selected</div>
                </div>
                <div>
                  <div className="text-sm text-gray-400">Custom Documents</div>
                  <div className="text-white">{documents.filter(d => d.status === 'ready').length} ready</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={onNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {hasContent ? 'Continue with Knowledge Base' : 'Skip for Now'}
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
