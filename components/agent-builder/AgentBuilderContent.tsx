"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight, ChevronLeft, Info, Play, Search, Plus, Check, Loader2 } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
// Removed direct SDK imports - will use API routes instead
import voicesData from './retellai-voices-list.json';
import { AgentTemplate } from '@/lib/agent-templates';
import TemplateSelectionStep from './TemplateSelectionStep';
import SmartBehaviorStep from './SmartBehaviorStep';
import KnowledgeBaseStep from './KnowledgeBaseStep';
import AgentCreationStep from './AgentCreationStep';

// Define the steps in the agent creation process
const STEPS = {
  WELCOME: 'welcome',
  TEMPLATE_SELECTION: 1,
  BASIC_INFO: 2,
  VOICE_SELECTION: 3,
  BEHAVIOR: 4,
  KNOWLEDGE_BASE: 5,
  TRAINING: 6,
  CREATION: 7
};

// Define the voice interface based on the JSON structure
interface Voice {
  voice_id: string;
  voice_type: string;
  standard_voice_type?: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

// Define the agent data interface
interface AgentData {
  name: string;
  description: string;
  voiceId: string;
  behavior?: string;
  knowledgeBase?: string[];
  trainingExamples?: string[];
}

interface Agent {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive";
  lastModified: string;
  knowledgeBases: number;
  totalCalls: number;
  voice?: string;
  phone?: string;
  editedBy?: string;
  type?: string;
  voiceId?: string;
  behavior?: string;
}

interface AgentBuilderContentProps {
  mode: "create" | "edit";
  initialData?: Agent | null;
  onSave: (data: Agent) => void;
  onStepChange: (step: string | number) => void;
}

export default function AgentBuilderContent({
  mode,
  initialData,
  onSave,
  onStepChange
}: AgentBuilderContentProps) {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<string | number>(STEPS.WELCOME);
  const [agentData, setAgentData] = useState<AgentData>({
    name: '',
    description: '',
    voiceId: '',
    behavior: '',
    knowledgeBase: [],
    trainingExamples: []
  });
  const [activeProvider, setActiveProvider] = useState('elevenlabs');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredVoices, setFilteredVoices] = useState<Voice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioRef, setAudioRef] = useState<HTMLAudioElement | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);
  const [formData, setFormData] = useState<Partial<Agent>>(() => ({
    name: initialData?.name || "",
    description: initialData?.description || "",
    type: initialData?.type || "Single Prompt",
    status: initialData?.status || "inactive",
    behavior: initialData?.behavior || "",
    phone: initialData?.phone || "",
    voice: initialData?.voice || "",
    voiceId: initialData?.voiceId || "",
  }));

  // Removed RetellAI SDK state - using API routes instead

  // Initialize audio reference - only run once on mount
  useEffect(() => {
    const audio = new Audio();
    setAudioRef(audio);

    // Cleanup function
    return () => {
      audio.pause();
      audio.src = '';
    };
  }, []);

  // Removed RetellAI data fetching - using static voice data for now

  // Load saved data from localStorage on initial load
  useEffect(() => {
    const savedAgentData = localStorage.getItem('agentBuilderData');
    const savedStep = localStorage.getItem('agentBuilderStep');

    if (savedAgentData) {
      const parsedData = JSON.parse(savedAgentData);
      setAgentData(parsedData);

      // If there's a saved voice ID, find and set the selected voice
      if (parsedData.voiceId) {
        const voice = voicesData.find((v: Voice) => v.voice_id === parsedData.voiceId);
        if (voice) {
          setSelectedVoice(voice);
        }
      }
    }

    if (savedStep) {
      setCurrentStep(savedStep);
    }
  }, []);

  // Filter voices based on search query and active provider
  useEffect(() => {
    const query = searchQuery.toLowerCase().trim();

    const voices = voicesData.filter((voice: Voice) => {
      // First filter by provider
      if (activeProvider && voice.provider !== activeProvider) return false;

      // If no search query, include all voices from the selected provider
      if (!query) return true;

      // Search across all relevant voice properties
      return (
        voice.voice_name.toLowerCase().includes(query) ||
        voice.voice_id.toLowerCase().includes(query) ||
        (voice.provider && voice.provider.toLowerCase().includes(query)) ||
        (voice.accent && voice.accent.toLowerCase().includes(query)) ||
        (voice.gender && voice.gender.toLowerCase().includes(query)) ||
        (voice.age && voice.age.toLowerCase().includes(query)) ||
        (voice.standard_voice_type && voice.standard_voice_type.toLowerCase().includes(query)) ||
        (voice.voice_type && voice.voice_type.toLowerCase().includes(query))
      );
    });

    setFilteredVoices(voices);
  }, [searchQuery, activeProvider]);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('agentBuilderData', JSON.stringify(agentData));
  }, [agentData]);

  // Save current step to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('agentBuilderStep', String(currentStep));
  }, [currentStep]);

  // Update parent component when step changes
  useEffect(() => {
    onStepChange(currentStep);
  }, [currentStep, onStepChange]);

  const handleVoiceChange = (voice: Voice) => {
    setSelectedVoice(voice);
    setAgentData(prev => ({ ...prev, voiceId: voice.voice_id }));

    // Automatically proceed to the next step after a short delay
    // This gives the user a moment to see their selection before moving on
    setTimeout(() => {
      handleNext();
    }, 500);
  };

  const handleNext = () => {
    if (currentStep === STEPS.WELCOME) {
      setCurrentStep(STEPS.TEMPLATE_SELECTION);
    } else {
      setCurrentStep(prev => typeof prev === 'number' ? prev + 1 : STEPS.TEMPLATE_SELECTION);
    }
  };

  const handleBack = () => {
    if (currentStep === STEPS.TEMPLATE_SELECTION) {
      setCurrentStep(STEPS.WELCOME);
    } else {
      setCurrentStep(prev => typeof prev === 'number' ? prev - 1 : STEPS.TEMPLATE_SELECTION);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleProviderChange = (provider: string) => {
    setActiveProvider(provider);
  };

  const playVoiceSample = (url: string) => {
    if (audioRef) {
      if (isPlaying) {
        audioRef.pause();
        setIsPlaying(false);
      } else {
        audioRef.src = url;
        audioRef.play();
        setIsPlaying(true);

        audioRef.onended = () => {
          setIsPlaying(false);
        };
      }
    }
  };

  const handleCreateAgent = async () => {
    setIsCreatingAgent(true);

    try {
      // Create the agent data object (local only for now)
      const agentData = {
        ...initialData,
        ...formData,
        id: initialData?.id || crypto.randomUUID(),
        lastModified: new Date().toISOString().split('T')[0],
        knowledgeBases: initialData?.knowledgeBases || 0,
        totalCalls: initialData?.totalCalls || 0,
        editedBy: new Date().toLocaleString(),
      } as Agent;

      // Call the parent's onSave function
      onSave(agentData);

      // Show success message
      toast({
        title: mode === "create" ? "Agent created successfully!" : "Agent updated successfully!",
        description: `Your AI agent "${formData.name}" has been ${mode === "create" ? "created" : "updated"}.`,
        duration: 5000,
      });

      // Clear localStorage
      localStorage.removeItem('agentBuilderData');
      localStorage.removeItem('agentBuilderStep');

    } catch (error) {
      console.error('Error creating agent:', error);
      toast({
        title: `Failed to ${mode === "create" ? "create" : "update"} agent`,
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsCreatingAgent(false);
    }
  };

  const renderWelcomeScreen = () => (
    <div className="relative z-10 max-w-3xl mx-auto text-center py-12">
      <div className="mb-8">
        <div className="inline-block p-4 rounded-full bg-blue-900/30 mb-4">
          <svg
            width="64"
            height="64"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-blue-400"
          >
            <path
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"
              fill="currentColor"
            />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-white mb-2">Welcome to the AI Agent Laboratory</h3>
        <p className="text-gray-300 mb-6">
          Here you&apos;ll create your own AI voice assistant to help with insurance tasks.
          Follow the steps to bring your assistant to life.
        </p>
        <div className="flex flex-col space-y-4 text-left bg-gray-900/50 p-6 rounded-lg border border-gray-800">
          <h4 className="text-lg font-medium text-white">The creation process includes:</h4>
          <div className="space-y-2">
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">1</div>
              <p className="text-gray-300">Choose a template or start from scratch</p>
            </div>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">2</div>
              <p className="text-gray-300">Basic information (name and description)</p>
            </div>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">3</div>
              <p className="text-gray-300">Voice selection</p>
            </div>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">4</div>
              <p className="text-gray-300">Smart behavior configuration</p>
            </div>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">5</div>
              <p className="text-gray-300">Knowledge base setup (documents, policies)</p>
            </div>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">6</div>
              <p className="text-gray-300">Training with example conversations</p>
            </div>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 text-xs text-blue-400">7</div>
              <p className="text-gray-300">Review and deployment</p>
            </div>
          </div>
        </div>
      </div>

      <Button
        className="px-6 py-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
        onClick={handleNext}
      >
        Start with Step 1: Choose Template
        <ChevronRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  );

  const renderBasicInfoStep = () => (
    <div className="relative z-10 max-w-3xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 2: Basic Information</h3>
        <p className="text-gray-300 mb-6">
          {selectedTemplate
            ? `Let's customize the details for your ${selectedTemplate.name} agent.`
            : "Let's start by setting up the basic details for your AI agent."
          }
        </p>

        {selectedTemplate && (
          <div className="bg-blue-900/20 border border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <span className="text-xl mr-2">{selectedTemplate.icon}</span>
              <div>
                <h4 className="text-white font-medium">Using Template: {selectedTemplate.name}</h4>
                <p className="text-gray-300 text-sm">{selectedTemplate.description}</p>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center">
              <Label htmlFor="name" className="text-white">Agent Name</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-gray-400 ml-2 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-80">Choose a name that reflects the agent&apos;s purpose. This name will be used in the dashboard and reports.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Input
              id="name"
              name="name"
              placeholder="e.g., Insurance Advisor"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="bg-gray-900 border-gray-700 text-white"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center">
              <Label htmlFor="description" className="text-white">Description</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-gray-400 ml-2 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-80">Describe the agent&apos;s purpose and capabilities. This helps team members understand what the agent does.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Textarea
              id="description"
              name="description"
              placeholder="e.g., This agent helps customers understand their insurance policy options and answers common questions."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="bg-gray-900 border-gray-700 text-white min-h-[150px]"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
          disabled={!formData.name || !formData.description}
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderVoiceSelectionStep = () => (
    <div className="relative z-10 max-w-4xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 3: Select Voice</h3>
        <p className="text-gray-300 mb-6">
          Choose a voice that best represents your brand and will resonate with your customers.
        </p>

        <div className="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden">
          {/* Voice selection interface */}
          <div className="p-4 border-b border-gray-800">
            <Tabs defaultValue="elevenlabs" className="w-full">
              <TabsList className="bg-gray-800 mb-4">
                <TabsTrigger
                  value="elevenlabs"
                  onClick={() => handleProviderChange('elevenlabs')}
                  className="data-[state=active]:bg-gray-700"
                >
                  Elevenlabs
                </TabsTrigger>
                <TabsTrigger
                  value="play"
                  onClick={() => handleProviderChange('play')}
                  className="data-[state=active]:bg-gray-700"
                >
                  PlayHT
                </TabsTrigger>
                <TabsTrigger
                  value="openai"
                  onClick={() => handleProviderChange('openai')}
                  className="data-[state=active]:bg-gray-700"
                >
                  OpenAI
                </TabsTrigger>
              </TabsList>

              <div className="flex flex-wrap gap-4 mb-4">
                <Button variant="outline" className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add custom voice
                </Button>

                <div className="flex-1 flex gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      placeholder="Search by name, gender, accent, age..."
                      className="pl-8 bg-gray-800 border-gray-700 text-white"
                      value={searchQuery}
                      onChange={handleSearchChange}
                    />
                    {searchQuery && (
                      <button
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 hover:text-gray-300"
                        onClick={() => setSearchQuery('')}
                        aria-label="Clear search"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                    )}
                  </div>
                  {filteredVoices.length > 0 && searchQuery && (
                    <div className="flex items-center text-sm text-gray-400">
                      {filteredVoices.length} results
                    </div>
                  )}
                </div>
              </div>

              {/* Voice list table */}
              <div className="bg-gray-800 rounded-md overflow-hidden">
                <div className="grid grid-cols-12 gap-2 p-3 border-b border-gray-700 text-sm font-medium text-gray-400 sticky top-0 bg-gray-800 z-10">
                  <div className="col-span-1"></div>
                  <div className="col-span-3">Voice</div>
                  <div className="col-span-6">Trait</div>
                  <div className="col-span-2">Voice ID</div>
                </div>

                <div className="max-h-[300px] overflow-y-auto">
                  {filteredVoices.length > 0 ? (
                    filteredVoices.map((voice) => (
                      <div
                        key={voice.voice_id}
                        className={`grid grid-cols-12 gap-2 p-3 border-b border-gray-700 hover:bg-gray-700 cursor-pointer relative ${
                          selectedVoice?.voice_id === voice.voice_id ? 'bg-gray-700 ring-2 ring-blue-500 ring-inset' : ''
                        }`}
                        onClick={() => handleVoiceChange(voice)}
                      >
                        {selectedVoice?.voice_id === voice.voice_id && (
                          <div className="absolute right-2 top-2 bg-blue-500 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                          </div>
                        )}
                        <div className="col-span-1 flex items-center justify-center">
                          <button
                            className="p-1 rounded-full hover:bg-gray-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              playVoiceSample(voice.preview_audio_url);
                            }}
                          >
                            <Play className="h-4 w-4 text-blue-400" />
                          </button>
                        </div>
                        <div className="col-span-3 flex items-center">
                          <div className="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-600">
                            {voice.avatar_url && (
                              <Image
                                src={voice.avatar_url}
                                alt={voice.voice_name}
                                width={32}
                                height={32}
                              />
                            )}
                          </div>
                          <span className="text-white">{voice.voice_name}</span>
                        </div>
                        <div className="col-span-6 flex items-center gap-2">
                          {voice.accent && (
                            <span className="px-2 py-1 bg-gray-700 text-xs rounded-md text-gray-300">
                              {voice.accent}
                            </span>
                          )}
                          {voice.age && (
                            <span className="px-2 py-1 bg-gray-700 text-xs rounded-md text-gray-300">
                              {voice.age}
                            </span>
                          )}
                          {voice.gender && (
                            <span className="px-2 py-1 bg-gray-700 text-xs rounded-md text-gray-300">
                              {voice.gender}
                            </span>
                          )}
                          {voice.standard_voice_type === "retell" && (
                            <span className="px-2 py-1 bg-blue-900/50 text-xs rounded-md text-blue-300">
                              Retell
                            </span>
                          )}
                        </div>
                        <div className="col-span-2 flex items-center text-gray-400 text-sm">
                          {voice.voice_id}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center">
                      <div className="text-gray-400 mb-2">No voices found matching &quot;{searchQuery}&quot;</div>
                      <p className="text-sm text-gray-500">Try adjusting your search or filter criteria</p>
                    </div>
                  )}
                </div>
              </div>
            </Tabs>
          </div>

          {/* Selected voice preview */}
          {selectedVoice && (
            <div className="mt-4">
              <div className="p-4 bg-gray-800/50 border border-gray-700 rounded-md">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gray-700">
                    {selectedVoice.avatar_url && (
                      <Image
                        src={selectedVoice.avatar_url}
                        alt={selectedVoice.voice_name}
                        width={40}
                        height={40}
                      />
                    )}
                  </div>
                  <div>
                    <div className="flex items-center">
                      <h4 className="text-white font-medium">{selectedVoice.voice_name}</h4>
                      <span className="ml-2 px-2 py-0.5 bg-blue-500/20 text-blue-400 text-xs rounded-md">
                        Selected Voice
                      </span>
                    </div>
                    <div className="flex gap-2 mt-1">
                      {selectedVoice.accent && (
                        <span className="px-2 py-0.5 bg-gray-700 text-xs rounded-md text-gray-300">
                          {selectedVoice.accent}
                        </span>
                      )}
                      {selectedVoice.gender && (
                        <span className="px-2 py-0.5 bg-gray-700 text-xs rounded-md text-gray-300">
                          {selectedVoice.gender}
                        </span>
                      )}
                      {selectedVoice.age && (
                        <span className="px-2 py-0.5 bg-gray-700 text-xs rounded-md text-gray-300">
                          {selectedVoice.age}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="ml-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-gray-700 hover:bg-gray-600 border-gray-600 text-white"
                      onClick={() => playVoiceSample(selectedVoice.preview_audio_url)}
                    >
                      <Play className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between items-center mt-6">
        <div className="text-sm text-gray-400">
          Click directly on a voice to select it and proceed to the next step
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleBack}
            className="border-gray-700 text-white hover:bg-gray-800"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleNext}
            className={`${formData.voiceId ? 'bg-blue-600 hover:bg-blue-700 animate-pulse' : 'bg-gray-700'} text-white transition-colors`}
            disabled={!formData.voiceId}
          >
            {formData.voiceId ? 'Continue with Selected Voice' : 'Select a Voice to Continue'}
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  const renderBehaviorStep = () => (
    <div className="relative z-10 max-w-3xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 3: Behavior Configuration</h3>
        <p className="text-gray-300 mb-6">
          Define how your AI agent should behave and respond to users.
        </p>

        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center">
              <Label htmlFor="behavior" className="text-white">System Prompt</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-gray-400 ml-2 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-80">The system prompt defines the personality, knowledge, and behavior of your AI agent. Be specific about its role and how it should interact.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Textarea
              id="behavior"
              name="behavior"
              placeholder="You are an AI assistant for an insurance company. You help customers understand their policy options and answer questions about coverage, claims, and benefits. Be friendly, professional, and concise in your responses."
              value={formData.behavior}
              onChange={(e) => setFormData(prev => ({ ...prev, behavior: e.target.value }))}
              className="bg-gray-900 border-gray-700 text-white min-h-[200px]"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
          disabled={!formData.behavior}
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderKnowledgeBaseStep = () => (
    <div className="relative z-10 max-w-3xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 4: Knowledge Base</h3>
        <p className="text-gray-300 mb-6">
          Add documents and information sources for your AI agent to reference.
        </p>

        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
          <p className="text-gray-300 mb-4">
            This feature will be implemented in a future update. For now, your agent will use its built-in knowledge.
          </p>

          <Button
            variant="outline"
            className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
            disabled
          >
            <Plus className="h-4 w-4 mr-2" />
            Add document (coming soon)
          </Button>
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderTrainingStep = () => (
    <div className="relative z-10 max-w-3xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 5: Training</h3>
        <p className="text-gray-300 mb-6">
          Provide example conversations to help your AI agent learn how to respond.
        </p>

        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
          <p className="text-gray-300 mb-4">
            This feature will be implemented in a future update. For now, your agent will use default training examples.
          </p>

          <Button
            variant="outline"
            className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
            disabled
          >
            <Plus className="h-4 w-4 mr-2" />
            Add training example (coming soon)
          </Button>
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderReviewStep = () => (
    <div className="relative z-10 max-w-3xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 6: Review and Create</h3>
        <p className="text-gray-300 mb-6">
          Review your AI agent configuration before creating it.
        </p>

        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 space-y-6">
          <div>
            <h4 className="text-lg font-medium text-white mb-2">Basic Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-400 text-sm">Name</p>
                <p className="text-white">{formData.name}</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Description</p>
                <p className="text-white">{formData.description}</p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-4">
            <h4 className="text-lg font-medium text-white mb-2">Voice</h4>
            {selectedVoice && (
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gray-700">
                  {selectedVoice.avatar_url && (
                    <Image
                      src={selectedVoice.avatar_url}
                      alt={selectedVoice.voice_name}
                      width={40}
                      height={40}
                    />
                  )}
                </div>
                <div>
                  <p className="text-white">{selectedVoice.voice_name}</p>
                  <div className="flex gap-2 mt-1">
                    {selectedVoice.accent && (
                      <span className="px-2 py-0.5 bg-gray-700 text-xs rounded-md text-gray-300">
                        {selectedVoice.accent}
                      </span>
                    )}
                    {selectedVoice.gender && (
                      <span className="px-2 py-0.5 bg-gray-700 text-xs rounded-md text-gray-300">
                        {selectedVoice.gender}
                      </span>
                    )}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-auto bg-gray-700 hover:bg-gray-600 border-gray-600 text-white"
                  onClick={() => playVoiceSample(selectedVoice.preview_audio_url)}
                >
                  <Play className="h-3 w-3 mr-1" />
                  Preview
                </Button>
              </div>
            )}
          </div>

          <div className="border-t border-gray-800 pt-4">
            <h4 className="text-lg font-medium text-white mb-2">Behavior</h4>
            <p className="text-white bg-gray-800 p-3 rounded-md">
              {formData.behavior || "Default behavior: Helpful AI assistant for insurance company."}
            </p>
          </div>

          <div className="border-t border-gray-800 pt-4">
            <h4 className="text-lg font-medium text-white mb-2">Technical Configuration</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-400 text-sm">LLM Model</p>
                <p className="text-white">GPT-4o</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Temperature</p>
                <p className="text-white">0.7</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Max Tokens</p>
                <p className="text-white">1000</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Provider</p>
                <p className="text-white">Retell AI</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleCreateAgent}
          className="bg-green-600 hover:bg-green-700 text-white px-8 py-6 text-lg font-medium animate-pulse hover:animate-none shadow-lg"
          disabled={isCreatingAgent}
        >
          {isCreatingAgent ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Creating Agent...
            </>
          ) : (
            <>
              <Check className="mr-2 h-5 w-5" />
              Create My AI Agent
            </>
          )}
        </Button>
      </div>

      {/* Final call-to-action */}
      <div className="mt-8 text-center bg-blue-900/30 p-6 rounded-lg border border-blue-800">
        <h4 className="text-lg font-medium text-white mb-2">Ready to bring your AI agent to life?</h4>
        <p className="text-gray-300 mb-4">
          Click the &#34;Create My AI Agent&#34; button above to finalize your configuration and deploy your AI assistant.
          Once created, you&#39;ll be able to interact with your agent and share it with others.
        </p>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full relative">
      {/* Laboratory background */}
      <div className="fixed inset-0 opacity-20 pointer-events-none">
        <div className="relative w-full h-full">
          <Image
            src="/images/laboratory-background.jpg"
            alt="Laboratory"
            fill
            priority
            sizes="100vw"
            style={{
              objectFit: 'cover',
              objectPosition: 'center'
            }}
          />
        </div>
      </div>

      {/* Content based on current step */}
      <div className="relative z-10 pb-16">
        {currentStep === STEPS.WELCOME && renderWelcomeScreen()}
        {currentStep === STEPS.TEMPLATE_SELECTION && (
          <TemplateSelectionStep
            onSelectTemplate={setSelectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
            selectedTemplate={selectedTemplate}
          />
        )}
        {currentStep === STEPS.BASIC_INFO && renderBasicInfoStep()}
        {currentStep === STEPS.VOICE_SELECTION && renderVoiceSelectionStep()}
        {currentStep === STEPS.BEHAVIOR && (
          <SmartBehaviorStep
            selectedTemplate={selectedTemplate}
            behavior={formData.behavior || ''}
            onBehaviorChange={(behavior) => setFormData(prev => ({ ...prev, behavior }))}
            onNext={handleNext}
            onBack={handleBack}
          />
        )}
        {currentStep === STEPS.KNOWLEDGE_BASE && (
          <KnowledgeBaseStep
            selectedTemplate={selectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
          />
        )}
        {currentStep === STEPS.TRAINING && renderTrainingStep()}
        {currentStep === STEPS.CREATION && (
          <AgentCreationStep
            agentData={{
              name: formData.name || '',
              description: formData.description || '',
              voiceId: formData.voiceId || '',
              behavior: formData.behavior || '',
              selectedTemplate
            }}
            onSuccess={(agentId) => {
              // Handle successful creation
              console.log('Agent created with ID:', agentId);
              // You could redirect to the agent details page or close the modal
            }}
            onBack={handleBack}
          />
        )}
      </div>
    </div>
  );
}