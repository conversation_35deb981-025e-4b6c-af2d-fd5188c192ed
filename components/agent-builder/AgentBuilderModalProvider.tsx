"use client";

import React from 'react';
import AgentBuilderModal from './AgentBuilderModal';
import AgentBuilderContent from './AgentBuilderContent';

interface Agent {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive";
  lastModified: string;
  knowledgeBases: number;
  totalCalls: number;
  voice?: string;
  phone?: string;
  editedBy?: string;
  type?: string;
  voiceId?: string;
  behavior?: string;
}

interface AgentBuilderModalProviderProps {
  children: React.ReactElement;
  isOpen: boolean;
  onClose: () => void;
  mode?: "create" | "edit";
  agent?: Agent | null;
  onSave?: (data: Agent) => void;
}

export default function AgentBuilderModalProvider({
  children,
  isOpen,
  onClose,
  mode = "create",
  agent = null,
  onSave = () => {}
}: AgentBuilderModalProviderProps) {
  const [currentStep, setCurrentStep] = React.useState<string | number>(1);

  return (
    <>
      {children}
      <AgentBuilderModal
        isOpen={isOpen}
        onClose={onClose}
        mode={mode}
        agent={agent}
        currentStep={currentStep}
      >
        <AgentBuilderContent
          mode={mode}
          initialData={agent}
          onSave={onSave}
          onStepChange={setCurrentStep}
        />
      </AgentBuilderModal>
    </>
  );
} 