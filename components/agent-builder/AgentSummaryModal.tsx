"use client";

import React, { useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Play, Pause, Edit, Trash2 } from "lucide-react"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import { cn } from "@/lib/utils"
import DeleteAgentDialog from "./DeleteAgentDialog"

interface Agent {
  id: string
  agent_id: string
  agent_name: string
  name: string
  description: string
  status: "active" | "inactive"
  lastModified: string
  knowledgeBases: number
  totalCalls: number
  voice?: string
  phone?: string
  editedBy?: string
  type?: string
  voiceId?: string
  voice_id?: string
  behavior?: string
}

interface Voice {
  voice_id: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

interface AgentSummaryModalProps {
  isOpen: boolean
  onClose: () => void
  agent: Agent | null
  voiceData: Voice | null
  onPlayVoiceSample?: (url: string) => void
  onEditAgent?: () => void
  onDeleteAgent?: (agentId: string) => Promise<void>
}

export default function AgentSummaryModal({
  isOpen,
  onClose,
  agent,
  voiceData,
  onPlayVoiceSample,
  onEditAgent,
  onDeleteAgent
}: AgentSummaryModalProps) {
  const overlayRef = useRef<HTMLDivElement>(null)
  const [isPlaying, setIsPlaying] = React.useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false)

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose()
    }
    window.addEventListener("keydown", handleEscape)
    return () => window.removeEventListener("keydown", handleEscape)
  }, [onClose])

  // Close modal when clicking outside
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === overlayRef.current) onClose()
  }

  const handlePlayVoice = () => {
    if (voiceData && onPlayVoiceSample) {
      onPlayVoiceSample(voiceData.preview_audio_url)
      setIsPlaying(!isPlaying)
    }
  }

  const handleDeleteAgent = async (agentId: string) => {
    if (onDeleteAgent) {
      await onDeleteAgent(agentId)
      onClose() // Close the summary modal after successful deletion
    }
  }

  const handleDeleteClick = () => {
    setShowDeleteDialog(true)
  }

  if (!agent) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <div
          ref={overlayRef}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
          onClick={handleOverlayClick}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "relative w-full max-w-3xl max-h-[90vh] overflow-auto rounded-lg shadow-2xl",
              "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800",
              "backdrop-filter backdrop-blur-xl"
            )}
          >
            {/* Header with close button */}
            <div className="sticky top-0 z-10 flex items-center justify-between border-b bg-white dark:bg-gray-900 p-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant={agent.status === "active" ? "default" : "secondary"}
                  className={cn(
                    "px-2 py-0.5",
                    agent.status === "active" && "bg-green-500/20 text-green-600 hover:bg-green-500/30 border-green-500/10"
                  )}
                >
                  {agent.status === "active" ? "Active" : "Inactive"}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Last modified: {agent.lastModified}
                </span>
              </div>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6 bg-white dark:bg-gray-900">
              <h2 className="text-2xl font-bold mb-2">{agent.name}</h2>
              <p className="text-muted-foreground mb-6">{agent.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="space-y-2 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Agent Type:</span>
                      <span>{agent.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Phone Number:</span>
                      <span>{agent.phone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Knowledge Bases:</span>
                      <span>{agent.knowledgeBases}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Calls:</span>
                      <span>{agent.totalCalls}</span>
                    </div>
                  </div>
                </div>

                {/* Voice Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Voice</h3>
                  {voiceData ? (
                    <div className="flex items-center gap-4 p-3 border rounded-md">
                      <Avatar className="h-12 w-12 border border-gray-200 shadow-sm">
                        <AvatarImage
                          src={voiceData.avatar_url}
                          alt={voiceData.voice_name}
                          className="object-cover"
                        />
                        <AvatarFallback className="text-sm font-medium bg-gray-100 text-gray-800">
                          {voiceData.voice_name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">{voiceData.voice_name}</div>
                        <div className="text-sm text-muted-foreground">
                          {[
                            voiceData.accent,
                            voiceData.gender,
                            voiceData.age
                          ].filter(Boolean).join(", ")}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={handlePlayVoice}
                      >
                        {isPlaying ? (
                          <><Pause className="h-3 w-3" /> Pause</>
                        ) : (
                          <><Play className="h-3 w-3" /> Preview</>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">No voice selected</div>
                  )}
                </div>
              </div>

              {/* Behavior */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Behavior</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="whitespace-pre-wrap">{agent.behavior || "No behavior defined"}</p>
                </div>
              </div>

              {/* Knowledge Base */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Knowledge Base</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  {agent.knowledgeBases > 0 ? (
                    <div>
                      <p>{agent.knowledgeBases} knowledge base(s) connected</p>
                      {/* Knowledge base details would go here */}
                    </div>
                  ) : (
                    <p>No knowledge bases connected</p>
                  )}
                </div>
              </div>

              {/* Technical Configuration */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Technical Configuration</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Voice ID:</span>
                      <span>{agent.voice_id || agent.voiceId || "None"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Agent ID:</span>
                      <span>{agent.agent_id || agent.id}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer with actions */}
            <div className="sticky bottom-0 z-10 flex items-center justify-between border-t bg-white dark:bg-gray-900 p-4">
              <div className="flex items-center gap-2">
                {onDeleteAgent && (
                  <Button
                    variant="outline"
                    onClick={handleDeleteClick}
                    className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="h-4 w-4 mr-2" /> Delete Agent
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={onClose}>
                  Close
                </Button>
                {onEditAgent && (
                  <Button onClick={onEditAgent}>
                    <Edit className="h-4 w-4 mr-2" /> Edit Agent
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Delete Agent Dialog */}
      <DeleteAgentDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        agent={agent ? {
          agent_id: agent.agent_id || agent.id,
          agent_name: agent.agent_name || agent.name,
          description: agent.description,
          status: agent.status,
          voice_id: agent.voice_id || agent.voiceId,
          totalCalls: agent.totalCalls,
          knowledgeBases: agent.knowledgeBases
        } : null}
        onConfirmDelete={handleDeleteAgent}
      />
    </AnimatePresence>
  )
}