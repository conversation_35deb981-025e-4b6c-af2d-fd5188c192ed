"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Search, 
  Plus, 
  Volume2,
  Pause
} from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";
import voicesData from './retellai-voices-list.json';

interface Voice {
  voice_id: string;
  voice_type: string;
  standard_voice_type?: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

interface VoiceSelectionStepProps {
  selectedTemplate: AgentTemplate | null;
  selectedVoice: Voice | null;
  onVoiceSelect: (voice: Voice) => void;
  onNext: () => void;
  onBack: () => void;
}

export default function VoiceSelectionStep({
  selectedTemplate,
  selectedVoice,
  onVoiceSelect,
  onNext,
  onBack
}: VoiceSelectionStepProps) {
  const [activeProvider, setActiveProvider] = useState('elevenlabs');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredVoices, setFilteredVoices] = useState<Voice[]>([]);
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const [audioRef, setAudioRef] = useState<HTMLAudioElement | null>(null);

  // Initialize audio reference
  useEffect(() => {
    const audio = new Audio();
    setAudioRef(audio);
    return () => {
      audio.pause();
      audio.src = '';
    };
  }, []);

  // Filter voices based on search query and active provider
  useEffect(() => {
    const query = searchQuery.toLowerCase().trim();
    
    const voices = voicesData.filter((voice: Voice) => {
      // First filter by provider
      if (activeProvider && voice.provider !== activeProvider) return false;
      
      // If no search query, include all voices from the selected provider
      if (!query) return true;
      
      // Search across all relevant voice properties
      return (
        voice.voice_name.toLowerCase().includes(query) ||
        voice.voice_id.toLowerCase().includes(query) ||
        (voice.provider && voice.provider.toLowerCase().includes(query)) ||
        (voice.accent && voice.accent.toLowerCase().includes(query)) ||
        (voice.gender && voice.gender.toLowerCase().includes(query)) ||
        (voice.age && voice.age.toLowerCase().includes(query)) ||
        (voice.standard_voice_type && voice.standard_voice_type.toLowerCase().includes(query)) ||
        (voice.voice_type && voice.voice_type.toLowerCase().includes(query))
      );
    });

    setFilteredVoices(voices);
  }, [searchQuery, activeProvider]);

  const playVoiceSample = (voice: Voice) => {
    if (!audioRef) return;

    if (isPlaying === voice.voice_id) {
      audioRef.pause();
      setIsPlaying(null);
    } else {
      audioRef.src = voice.preview_audio_url;
      audioRef.play();
      setIsPlaying(voice.voice_id);

      audioRef.onended = () => {
        setIsPlaying(null);
      };
    }
  };

  const handleVoiceSelect = (voice: Voice) => {
    onVoiceSelect(voice);
  };

  const getSuggestedVoices = () => {
    if (!selectedTemplate?.suggestedVoices) return [];
    return voicesData.filter(voice => 
      selectedTemplate.suggestedVoices.includes(voice.voice_id)
    );
  };

  const suggestedVoices = getSuggestedVoices();

  return (
    <div className="max-w-6xl mx-auto py-8">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">Voice Selection</h2>
        <p className="text-gray-300 text-lg mb-6">
          Choose a voice that best represents your brand and will resonate with your customers.
        </p>

        {selectedTemplate && suggestedVoices.length > 0 && (
          <Card className="bg-blue-900/20 border-blue-800 mb-6">
            <CardHeader>
              <CardTitle className="text-white">Recommended Voices for {selectedTemplate.name}</CardTitle>
              <CardDescription className="text-gray-300">
                These voices work particularly well with your selected template
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {suggestedVoices.map((voice) => (
                  <Card 
                    key={voice.voice_id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                      selectedVoice?.voice_id === voice.voice_id 
                        ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                        : 'bg-gray-800 border-gray-700 hover:bg-gray-750'
                    }`}
                    onClick={() => handleVoiceSelect(voice)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-700 flex-shrink-0">
                          {voice.avatar_url && (
                            <Image
                              src={voice.avatar_url}
                              alt={voice.voice_name}
                              width={48}
                              height={48}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-white font-medium truncate">{voice.voice_name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            {voice.gender && (
                              <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                                {voice.gender}
                              </Badge>
                            )}
                            {voice.accent && (
                              <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                                {voice.accent}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            playVoiceSample(voice);
                          }}
                          className="flex-shrink-0"
                        >
                          {isPlaying === voice.voice_id ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">All Available Voices</CardTitle>
            <CardDescription className="text-gray-300">
              Browse all voices by provider or search for specific characteristics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeProvider} onValueChange={setActiveProvider} className="w-full">
              <TabsList className="bg-gray-800 mb-4">
                <TabsTrigger value="elevenlabs" className="data-[state=active]:bg-gray-700">
                  ElevenLabs
                </TabsTrigger>
                <TabsTrigger value="play" className="data-[state=active]:bg-gray-700">
                  PlayHT
                </TabsTrigger>
                <TabsTrigger value="openai" className="data-[state=active]:bg-gray-700">
                  OpenAI
                </TabsTrigger>
              </TabsList>

              <div className="flex flex-wrap gap-4 mb-6">
                <Button variant="outline" className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add custom voice
                </Button>

                <div className="flex-1 flex gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      placeholder="Search by name, gender, accent, age..."
                      className="pl-10 bg-gray-800 border-gray-700 text-white"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  {filteredVoices.length > 0 && searchQuery && (
                    <div className="flex items-center text-sm text-gray-400">
                      {filteredVoices.length} results
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg overflow-hidden">
                <div className="grid grid-cols-12 gap-2 p-3 border-b border-gray-700 text-sm font-medium text-gray-400 sticky top-0 bg-gray-800 z-10">
                  <div className="col-span-1"></div>
                  <div className="col-span-3">Voice</div>
                  <div className="col-span-6">Characteristics</div>
                  <div className="col-span-2">Voice ID</div>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {filteredVoices.length > 0 ? (
                    filteredVoices.map((voice) => (
                      <div
                        key={voice.voice_id}
                        className={`grid grid-cols-12 gap-2 p-3 border-b border-gray-700 hover:bg-gray-700 cursor-pointer relative ${
                          selectedVoice?.voice_id === voice.voice_id ? 'bg-gray-700 ring-2 ring-blue-500 ring-inset' : ''
                        }`}
                        onClick={() => handleVoiceSelect(voice)}
                      >
                        {selectedVoice?.voice_id === voice.voice_id && (
                          <div className="absolute right-2 top-2 bg-blue-500 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                          </div>
                        )}
                        <div className="col-span-1 flex items-center justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              playVoiceSample(voice);
                            }}
                            className="p-1 h-8 w-8"
                          >
                            {isPlaying === voice.voice_id ? (
                              <Pause className="h-4 w-4 text-blue-400" />
                            ) : (
                              <Play className="h-4 w-4 text-blue-400" />
                            )}
                          </Button>
                        </div>
                        <div className="col-span-3 flex items-center">
                          <div className="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-600 flex-shrink-0">
                            {voice.avatar_url && (
                              <Image
                                src={voice.avatar_url}
                                alt={voice.voice_name}
                                width={32}
                                height={32}
                                className="w-full h-full object-cover"
                              />
                            )}
                          </div>
                          <span className="text-white truncate">{voice.voice_name}</span>
                        </div>
                        <div className="col-span-6 flex items-center gap-2">
                          {voice.accent && (
                            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                              {voice.accent}
                            </Badge>
                          )}
                          {voice.age && (
                            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                              {voice.age}
                            </Badge>
                          )}
                          {voice.gender && (
                            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                              {voice.gender}
                            </Badge>
                          )}
                          {voice.standard_voice_type === "retell" && (
                            <Badge variant="outline" className="text-xs border-blue-600 text-blue-400">
                              Retell
                            </Badge>
                          )}
                        </div>
                        <div className="col-span-2 flex items-center text-gray-400 text-sm">
                          <span className="truncate">{voice.voice_id}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center">
                      <div className="text-gray-400 mb-2">No voices found matching "{searchQuery}"</div>
                      <p className="text-sm text-gray-500">Try adjusting your search or filter criteria</p>
                    </div>
                  )}
                </div>
              </div>
            </Tabs>
          </CardContent>
        </Card>

        {/* Selected Voice Preview */}
        {selectedVoice && (
          <Card className="bg-green-900/20 border-green-800 mt-6">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Volume2 className="h-5 w-5 mr-2" />
                Selected Voice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-700">
                    {selectedVoice.avatar_url && (
                      <Image
                        src={selectedVoice.avatar_url}
                        alt={selectedVoice.voice_name}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div>
                    <h4 className="text-white font-medium text-lg">{selectedVoice.voice_name}</h4>
                    <div className="flex gap-2 mt-1">
                      {selectedVoice.accent && (
                        <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                          {selectedVoice.accent}
                        </Badge>
                      )}
                      {selectedVoice.gender && (
                        <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                          {selectedVoice.gender}
                        </Badge>
                      )}
                      {selectedVoice.age && (
                        <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                          {selectedVoice.age}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => playVoiceSample(selectedVoice)}
                  className="bg-gray-700 hover:bg-gray-600 border-gray-600 text-white"
                >
                  {isPlaying === selectedVoice.voice_id ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Preview
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex justify-between items-center pt-6 border-t border-gray-800">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-gray-400 mb-2">
            {selectedVoice ? 'Voice selected! Ready to continue.' : 'Please select a voice to continue'}
          </p>
        </div>

        <Button
          onClick={onNext}
          disabled={!selectedVoice}
          className={`${
            selectedVoice 
              ? 'bg-blue-600 hover:bg-blue-700' 
              : 'bg-gray-700 cursor-not-allowed'
          } text-white`}
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
