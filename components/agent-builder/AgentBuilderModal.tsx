"use client";

import React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface Agent {
  id: string
  name: string
  description: string
  status: "active" | "inactive"
  lastModified: string
  knowledgeBases: number
  totalCalls: number
  voice?: string
  phone?: string
  editedBy?: string
  type?: string
  voiceId?: string
  behavior?: string
}

interface AgentBuilderModalProps {
  isOpen: boolean
  onClose: () => void
  mode: "create" | "edit"
  agent?: Agent | null
  children?: React.ReactNode
  currentStep?: string | number
}

export default function AgentBuilderModal({
  isOpen,
  onClose,
  mode = "create",
  agent,
  children,
  currentStep
}: AgentBuilderModalProps) {
  // Disable body scroll when modal is open
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  // Close modal on escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose()
    }
    window.addEventListener("keydown", handleEscape)
    return () => window.removeEventListener("keydown", handleEscape)
  }, [onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "relative w-full max-w-[90%] max-h-[90vh] overflow-auto rounded-lg shadow-2xl",
              "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800",
              "backdrop-filter backdrop-blur-xl"
            )}
          >
            {/* Header */}
            <div className="sticky top-0 z-10 flex flex-col border-b bg-background p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold">
                    {mode === "create" ? "Create AI Agent" : `Edit ${agent?.name || 'AI Agent'}`}
                  </h2>
                  <p className="text-sm text-muted-foreground mt-1">
                    {mode === "create" 
                      ? "Configure your new AI agent with custom capabilities and knowledge"
                      : `Modify the configuration and capabilities of ${agent?.name || 'this agent'}`
                    }
                  </p>
                </div>
                <Button variant="ghost" size="icon" onClick={onClose}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Progress indicator */}
              <div className="mt-4 flex items-center space-x-2">
                {[1, 2, 3, 4, 5, 6].map((step) => (
                  <div 
                    key={step}
                    className={cn(
                      "h-2 rounded-full transition-all duration-300",
                      step <= (agent ? 6 : 1) ? "bg-blue-500 w-8" : "bg-gray-700 w-4"
                    )}
                  />
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {children}
            </div>

            {/* Footer */}
            <div className="sticky bottom-0 z-10 flex items-center justify-end gap-2 border-t bg-background p-4">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                disabled={currentStep !== 6}
                className={cn(
                  currentStep === 6 ? "bg-green-600 hover:bg-green-700" : "bg-gray-600",
                  "transition-colors"
                )}
              >
                {mode === "create" ? "Create Agent" : "Save Changes"}
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
} 