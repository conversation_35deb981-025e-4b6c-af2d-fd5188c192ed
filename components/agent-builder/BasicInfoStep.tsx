"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Info, Lightbulb } from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface BasicInfoStepProps {
  selectedTemplate: AgentTemplate | null;
  formData: {
    name: string;
    description: string;
  };
  onFormDataChange: (data: { name: string; description: string }) => void;
  onNext: () => void;
  onBack: () => void;
}

export default function BasicInfoStep({
  selectedTemplate,
  formData,
  onFormDataChange,
  onNext,
  onBack
}: BasicInfoStepProps) {

  const handleInputChange = (field: 'name' | 'description', value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value
    });
  };

  const generateSuggestions = () => {
    if (!selectedTemplate) return;

    const suggestions = {
      'customer-service-general': {
        names: ['Customer Care Assistant', 'Support Specialist', 'Help Desk Agent'],
        descriptions: [
          'A friendly AI assistant that helps customers with general inquiries, policy questions, and provides excellent customer support.',
          'Specialized in handling customer questions about insurance policies, coverage details, and general support needs.',
          'Your dedicated customer service representative for insurance inquiries and support.'
        ]
      },
      'enrollment-specialist': {
        names: ['Benefits Enrollment Guide', 'Enrollment Assistant', 'Benefits Advisor'],
        descriptions: [
          'Expert guide for employee benefits enrollment, helping users understand options and make informed decisions.',
          'Specialized assistant for navigating benefits enrollment periods, plan comparisons, and enrollment processes.',
          'Your personal benefits consultant for enrollment guidance and plan selection.'
        ]
      },
      'claims-assistant': {
        names: ['Claims Processing Helper', 'Claims Support Agent', 'Claims Specialist'],
        descriptions: [
          'Compassionate assistant for filing claims, checking status, and guiding customers through the claims process.',
          'Expert in claims processing procedures, documentation requirements, and status updates.',
          'Your dedicated claims support specialist for filing and tracking insurance claims.'
        ]
      }
    };

    return suggestions[selectedTemplate.id as keyof typeof suggestions] || {
      names: ['Insurance Assistant', 'AI Helper', 'Support Agent'],
      descriptions: ['A helpful AI assistant for insurance-related tasks and customer support.']
    };
  };

  const suggestions = generateSuggestions();

  const applySuggestion = (type: 'name' | 'description', suggestion: string) => {
    handleInputChange(type, suggestion);
  };

  const isFormValid = formData.name.trim().length > 0 && formData.description.trim().length > 0;

  return (
    <div className="max-w-4xl mx-auto py-8">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-foreground mb-4">Basic Information</h2>
        <p className="text-muted-foreground text-lg mb-6">
          {selectedTemplate
            ? `Let's customize the details for your ${selectedTemplate.name} agent.`
            : "Let's start by setting up the basic details for your AI agent."
          }
        </p>

        {selectedTemplate && (
          <Card className="bg-primary/10 border-primary/20 mb-8">
            <CardHeader>
              <CardTitle className="text-foreground flex items-center">
                <span className="text-2xl mr-3">{selectedTemplate.icon}</span>
                Using Template: {selectedTemplate.name}
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                {selectedTemplate.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">
                  {selectedTemplate.category}
                </Badge>
                <Badge variant="outline">
                  {selectedTemplate.difficulty}
                </Badge>
                <Badge variant="secondary">
                  {selectedTemplate.estimatedSetupTime}
                </Badge>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Agent Name */}
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center">
                  <Label htmlFor="name" className="text-foreground">Agent Name</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground ml-2 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">Choose a name that reflects the agent's purpose. This name will be used in the dashboard, reports, and when customers interact with your agent.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  What should customers call your AI agent?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Input
                  id="name"
                  placeholder="e.g., Customer Care Assistant"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="text-lg h-12"
                />
                {selectedTemplate && suggestions && (
                  <div className="mt-3">
                    <p className="text-sm text-muted-foreground mb-2">Suggested names:</p>
                    <div className="flex flex-wrap gap-2">
                      {suggestions.names.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => applySuggestion('name', suggestion)}
                          className="text-xs"
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Agent Description */}
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center">
                  <Label htmlFor="description" className="text-foreground">Description</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground ml-2 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">Describe the agent's purpose and capabilities. This helps team members understand what the agent does and helps customers know how it can help them.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Explain what your agent does and how it helps customers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  id="description"
                  placeholder="e.g., This agent helps customers understand their insurance policy options and answers common questions about coverage, claims, and benefits."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="min-h-[120px]"
                />
                {selectedTemplate && suggestions && (
                  <div className="mt-3">
                    <p className="text-sm text-muted-foreground mb-2">Suggested descriptions:</p>
                    <div className="space-y-2">
                      {suggestions.descriptions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => applySuggestion('description', suggestion)}
                          className="text-xs h-auto p-2 text-left whitespace-normal"
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar Tips */}
          <div className="space-y-6">
            <Card className="bg-amber-50 border-amber-200 dark:bg-amber-950/20 dark:border-amber-800">
              <CardHeader>
                <CardTitle className="text-amber-700 dark:text-amber-400 flex items-center text-lg">
                  <Lightbulb className="h-5 w-5 mr-2" />
                  Pro Tips
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <strong>Agent Name:</strong> Keep it professional and descriptive. Customers will see this name during interactions.
                </div>
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <strong>Description:</strong> Be specific about what the agent can help with. This helps set proper expectations.
                </div>
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <strong>Template Benefits:</strong> Using a template gives you proven prompts and configurations that work well.
                </div>
              </CardContent>
            </Card>

            {selectedTemplate && (
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle className="text-foreground text-lg">Template Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {selectedTemplate.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-muted-foreground">
                        <div className="w-2 h-2 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button
          variant="outline"
          onClick={onBack}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-2">
            {isFormValid ? 'Ready to continue!' : 'Please fill in both fields to continue'}
          </p>
        </div>

        <Button
          onClick={onNext}
          disabled={!isFormValid}
          className={isFormValid ? 'bg-primary hover:bg-primary/90' : ''}
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
