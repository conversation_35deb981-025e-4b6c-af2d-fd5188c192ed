"use client";

import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"

interface CreateAgentButtonProps {
  variant?: "default" | "primary"
  onClick?: () => void
}

export default function CreateAgentButton({
  variant = "default",
  onClick
}: CreateAgentButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Navigate to the full-page agent builder
      router.push('/agent-builder/create');
    }
  };

  return (
    <Button
      onClick={handleClick}
      className={variant === "primary" ? "bg-blue-600 hover:bg-blue-700 text-white" : ""}
    >
      <Plus className="mr-2 h-4 w-4" />
      Create Agent
    </Button>
  )
}