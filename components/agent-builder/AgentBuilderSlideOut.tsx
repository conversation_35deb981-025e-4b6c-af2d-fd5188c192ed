"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ArrowLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AgentTemplate } from '@/lib/agent-templates';

interface AgentBuilderSlideOutProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (agentId: string) => void;
}

const STEPS = [
  { id: 1, name: 'Template', title: 'Choose Template' },
  { id: 2, name: 'Basic Info', title: 'Basic Information' },
  { id: 3, name: 'Voice', title: 'Voice Selection' },
  { id: 4, name: 'Behavior', title: 'Agent Behavior' },
  { id: 5, name: 'Knowledge', title: 'Knowledge Base' },
  { id: 6, name: 'Training', title: 'Training Examples' },
  { id: 7, name: 'Deploy', title: 'Create & Deploy' }
];

export default function AgentBuilderSlideOut({
  isOpen,
  onClose,
  onSuccess
}: AgentBuilderSlideOutProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);

  // Disable body scroll when open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / STEPS.length) * 100;
  const currentStepData = STEPS.find(step => step.id === currentStep);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* Slide-out Panel */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-6xl bg-gray-950 border-l border-gray-800 z-50 flex"
          >
            {/* Mini Sidebar */}
            <div className="w-64 bg-gray-900 border-r border-gray-800 flex flex-col">
              {/* Header */}
              <div className="p-4 border-b border-gray-800">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-lg font-bold text-white">Create Agent</h2>
                  <Button variant="ghost" size="sm" onClick={onClose}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>Step {currentStep} of {STEPS.length}</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              </div>

              {/* Steps */}
              <div className="flex-1 p-3 space-y-1">
                {STEPS.map((step) => {
                  const isActive = step.id === currentStep;
                  const isCompleted = step.id < currentStep;

                  return (
                    <div
                      key={step.id}
                      className={`p-3 rounded-md text-sm transition-all ${
                        isActive 
                          ? 'bg-blue-600 text-white' 
                          : isCompleted
                          ? 'bg-gray-800 text-gray-300'
                          : 'text-gray-500'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs ${
                          isCompleted ? 'bg-green-500 text-white' : 
                          isActive ? 'bg-white text-blue-600' : 'bg-gray-700 text-gray-400'
                        }`}>
                          {isCompleted ? '✓' : step.id}
                        </div>
                        <span className="font-medium">{step.name}</span>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Template Info */}
              {selectedTemplate && (
                <div className="p-3 border-t border-gray-800">
                  <div className="bg-blue-900/20 border border-blue-800 rounded-md p-2">
                    <div className="flex items-center space-x-2 mb-1">
                      <span>{selectedTemplate.icon}</span>
                      <span className="text-white text-sm font-medium">{selectedTemplate.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {selectedTemplate.category}
                    </Badge>
                  </div>
                </div>
              )}
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col">
              {/* Content Header */}
              <div className="p-6 border-b border-gray-800 bg-gray-900/50">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-2xl font-bold text-white">{currentStepData?.title}</h1>
                    <p className="text-gray-400 text-sm mt-1">
                      Step {currentStep} of {STEPS.length}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    {currentStep > 1 && (
                      <Button variant="outline" onClick={handleBack}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back
                      </Button>
                    )}
                    {currentStep < STEPS.length && (
                      <Button onClick={handleNext}>
                        Next
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Step Content */}
              <div className="flex-1 overflow-auto p-6 bg-gradient-to-br from-gray-950 via-gray-900 to-blue-950">
                {/* Render step content based on currentStep */}
                <div className="max-w-4xl mx-auto">
                  {currentStep === 1 && (
                    <div className="text-center py-12">
                      <h3 className="text-xl text-white mb-4">Choose Your Starting Point</h3>
                      <p className="text-gray-300 mb-8">Select a template or start from scratch</p>
                      {/* Template selection content */}
                    </div>
                  )}
                  
                  {currentStep === 2 && (
                    <div>
                      <h3 className="text-xl text-white mb-4">Basic Information</h3>
                      {/* Basic info form */}
                    </div>
                  )}

                  {/* Add other step content as needed */}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
