"use client";

import React, { useState, useEffect } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Trash2, AlertTriangle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface Agent {
  agent_id: string;
  agent_name: string;
  description?: string;
  status: "active" | "inactive";
  voice_id?: string;
  totalCalls?: number;
  knowledgeBases?: number;
}

interface DeleteAgentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agent: Agent | null;
  onConfirmDelete: (agentId: string) => Promise<void>;
}

export default function DeleteAgentDialog({
  isOpen,
  onClose,
  agent,
  onConfirmDelete
}: DeleteAgentDialogProps) {
  const [confirmationText, setConfirmationText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setConfirmationText('');
      setError(null);
      setIsDeleting(false);
    }
  }, [isOpen]);

  if (!agent) return null;

  const isConfirmationValid = confirmationText.trim() === agent.agent_name.trim();

  const handleDelete = async () => {
    if (!isConfirmationValid || isDeleting) return;

    try {
      setIsDeleting(true);
      setError(null);
      await onConfirmDelete(agent.agent_id);
      onClose();
    } catch (err) {
      console.error('Error deleting agent:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete agent');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    if (!isDeleting) {
      onClose();
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={handleCancel}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Agent
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p className="text-gray-600 dark:text-gray-300">
                You are about to permanently delete this AI agent. This action cannot be undone.
              </p>

              {/* Agent Info */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                    {agent.agent_name}
                  </h4>
                  <Badge 
                    variant={agent.status === "active" ? "default" : "secondary"}
                    className={cn(
                      agent.status === "active" && "bg-green-500/20 text-green-600 border-green-500/20"
                    )}
                  >
                    {agent.status}
                  </Badge>
                </div>
                {agent.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {agent.description}
                  </p>
                )}
                <div className="flex gap-4 text-xs text-gray-500 dark:text-gray-400">
                  {agent.totalCalls !== undefined && (
                    <span>Calls: {agent.totalCalls}</span>
                  )}
                  {agent.knowledgeBases !== undefined && (
                    <span>Knowledge Bases: {agent.knowledgeBases}</span>
                  )}
                </div>
              </div>

              {/* Warning */}
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-red-800 dark:text-red-200 mb-1">
                      This will permanently:
                    </p>
                    <ul className="text-red-700 dark:text-red-300 space-y-1">
                      <li>• Delete the agent from RetellAI</li>
                      <li>• Remove all agent versions</li>
                      <li>• Disconnect from phone numbers</li>
                      <li>• Stop all active calls</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Confirmation Input */}
              <div className="space-y-2">
                <Label htmlFor="confirmation" className="text-sm font-medium">
                  Type the agent name to confirm deletion:
                </Label>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {agent.agent_name}
                  </div>
                  <Input
                    id="confirmation"
                    value={confirmationText}
                    onChange={(e) => setConfirmationText(e.target.value)}
                    placeholder="Enter agent name exactly as shown above"
                    className={cn(
                      "font-mono",
                      isConfirmationValid && "border-green-500 focus:border-green-500",
                      confirmationText && !isConfirmationValid && "border-red-500 focus:border-red-500"
                    )}
                    disabled={isDeleting}
                    autoComplete="off"
                  />
                  {confirmationText && !isConfirmationValid && (
                    <p className="text-xs text-red-500">
                      Agent name doesn't match. Please type exactly: "{agent.agent_name}"
                    </p>
                  )}
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {error}
                  </p>
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={isDeleting}
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={!isConfirmationValid || isDeleting}
            className={cn(
              "bg-red-600 hover:bg-red-700 focus:ring-red-500",
              (!isConfirmationValid || isDeleting) && "opacity-50 cursor-not-allowed"
            )}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Agent
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
