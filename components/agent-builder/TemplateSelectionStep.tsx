"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Clock, Star, Users, Search, Sparkles, ChevronRight } from "lucide-react";
import { AgentTemplate, AGENT_TEMPLATES, getTemplatesByCategory, getPopularTemplates, getRecommendedTemplates } from "@/lib/agent-templates";

interface TemplateSelectionStepProps {
  onSelectTemplate: (template: AgentTemplate | null) => void;
  onNext: () => void;
  onBack: () => void;
  selectedTemplate: AgentTemplate | null;
}

export default function TemplateSelectionStep({
  onSelectTemplate,
  onNext,
  onBack,
  selectedTemplate
}: TemplateSelectionStepProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>('recommended');

  const filteredTemplates = React.useMemo(() => {
    let templates: AgentTemplate[] = [];

    switch (activeCategory) {
      case 'recommended':
        templates = getRecommendedTemplates();
        break;
      case 'popular':
        templates = getPopularTemplates();
        break;
      case 'customer_service':
        templates = getTemplatesByCategory('customer_service');
        break;
      case 'sales':
        templates = getTemplatesByCategory('sales');
        break;
      case 'claims':
        templates = getTemplatesByCategory('claims');
        break;
      case 'enrollment':
        templates = getTemplatesByCategory('enrollment');
        break;
      case 'all':
        templates = AGENT_TEMPLATES;
        break;
      default:
        templates = AGENT_TEMPLATES;
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      templates = templates.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return templates;
  }, [activeCategory, searchQuery]);

  const handleTemplateSelect = (template: AgentTemplate) => {
    onSelectTemplate(template);
  };

  const handleStartFromScratch = () => {
    onSelectTemplate(null);
    onNext();
  };

  return (
    <div className="relative z-10 max-w-6xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-foreground mb-2">Choose Your Starting Point</h3>
        <p className="text-muted-foreground mb-6">
          Select a pre-built template to get started quickly, or start from scratch for complete customization.
        </p>

        {/* Search and Categories */}
        <div className="mb-6">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates by name, description, or tags..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
            <TabsList className="mb-4 grid grid-cols-7 w-full">
              <TabsTrigger value="recommended">
                <Sparkles className="h-4 w-4 mr-1" />
                Recommended
              </TabsTrigger>
              <TabsTrigger value="popular">
                <Star className="h-4 w-4 mr-1" />
                Popular
              </TabsTrigger>
              <TabsTrigger value="customer_service">
                Customer Service
              </TabsTrigger>
              <TabsTrigger value="enrollment">
                Enrollment
              </TabsTrigger>
              <TabsTrigger value="claims">
                Claims
              </TabsTrigger>
              <TabsTrigger value="sales">
                Sales
              </TabsTrigger>
              <TabsTrigger value="all">
                All
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {filteredTemplates.map((template) => (
            <Card
              key={template.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedTemplate?.id === template.id
                  ? 'ring-2 ring-primary bg-primary/5'
                  : 'bg-card border-border hover:bg-muted/50'
              }`}
              onClick={() => handleTemplateSelect(template)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="text-2xl mb-2">{template.icon}</div>
                  <div className="flex gap-1">
                    {template.isRecommended && (
                      <Badge variant="secondary" className="text-xs">
                        Recommended
                      </Badge>
                    )}
                    {template.isPopular && (
                      <Badge variant="secondary" className="text-xs">
                        Popular
                      </Badge>
                    )}
                  </div>
                </div>
                <CardTitle className="text-lg text-foreground">{template.name}</CardTitle>
                <CardDescription className="text-muted-foreground text-sm">
                  {template.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center gap-2 mb-3">
                  <Badge variant="outline" className="text-xs">
                    {template.difficulty}
                  </Badge>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    {template.estimatedSetupTime}
                  </div>
                </div>

                <div className="space-y-2 mb-3">
                  <div className="text-xs text-muted-foreground font-medium">Key Features:</div>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {template.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <div className="w-1 h-1 bg-primary rounded-full mr-2"></div>
                        {feature}
                      </li>
                    ))}
                    {template.features.length > 3 && (
                      <li className="text-muted-foreground/70">+{template.features.length - 3} more...</li>
                    )}
                  </ul>
                </div>

                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Start from Scratch Option */}
        <Card className="bg-card border-dashed">
          <CardContent className="p-6 text-center">
            <div className="text-4xl mb-3">🛠️</div>
            <h4 className="text-lg font-medium text-foreground mb-2">Start from Scratch</h4>
            <p className="text-muted-foreground text-sm mb-4">
              Build your agent completely from the ground up with full customization control.
            </p>
            <Button
              variant="outline"
              onClick={handleStartFromScratch}
            >
              Start Custom Build
            </Button>
          </CardContent>
        </Card>

        {filteredTemplates.length === 0 && searchQuery && (
          <div className="text-center py-8">
            <div className="text-muted-foreground mb-2">No templates found matching "{searchQuery}"</div>
            <p className="text-sm text-muted-foreground/70">Try adjusting your search or browse by category</p>
          </div>
        )}
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onBack}
        >
          Back
        </Button>
        <Button
          onClick={onNext}
          disabled={!selectedTemplate}
        >
          {selectedTemplate ? `Continue with ${selectedTemplate.name}` : 'Select a Template'}
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
