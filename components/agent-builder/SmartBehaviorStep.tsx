"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Lightbulb, Settings, Eye, Wand2 } from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface SmartBehaviorStepProps {
  selectedTemplate: AgentTemplate | null;
  behavior: string;
  onBehaviorChange: (behavior: string) => void;
  onNext: () => void;
  onBack: () => void;
}

const PERSONALITY_TRAITS = [
  { id: 'friendly', label: 'Friendly & Warm', description: 'Approachable and welcoming tone' },
  { id: 'professional', label: 'Professional', description: 'Business-like and formal' },
  { id: 'empathetic', label: 'Empathetic', description: 'Understanding and compassionate' },
  { id: 'efficient', label: 'Efficient', description: 'Direct and to-the-point' },
  { id: 'patient', label: 'Patient', description: 'Takes time to explain things clearly' },
  { id: 'knowledgeable', label: 'Expert', description: 'Demonstrates deep expertise' }
];

const COMMUNICATION_STYLES = [
  { id: 'conversational', label: 'Conversational', description: 'Natural, flowing dialogue' },
  { id: 'structured', label: 'Structured', description: 'Organized, step-by-step approach' },
  { id: 'consultative', label: 'Consultative', description: 'Advisory and guidance-focused' },
  { id: 'supportive', label: 'Supportive', description: 'Encouraging and helpful' }
];

export default function SmartBehaviorStep({
  selectedTemplate,
  behavior,
  onBehaviorChange,
  onNext,
  onBack
}: SmartBehaviorStepProps) {
  const [activeTab, setActiveTab] = useState('guided');
  const [selectedTraits, setSelectedTraits] = useState<string[]>(['friendly', 'professional']);
  const [selectedStyle, setSelectedStyle] = useState<string>('conversational');
  const [customInstructions, setCustomInstructions] = useState('');

  const handleTraitToggle = (traitId: string) => {
    setSelectedTraits(prev => 
      prev.includes(traitId) 
        ? prev.filter(id => id !== traitId)
        : [...prev, traitId]
    );
  };

  const generateSmartPrompt = () => {
    const traits = selectedTraits.map(id => 
      PERSONALITY_TRAITS.find(trait => trait.id === id)?.label
    ).join(', ');
    
    const style = COMMUNICATION_STYLES.find(s => s.id === selectedStyle)?.label;
    
    let basePrompt = selectedTemplate?.systemPrompt || `You are a helpful AI assistant for an insurance company.`;
    
    const enhancedPrompt = `${basePrompt}

Personality & Communication Style:
- Maintain a ${traits.toLowerCase()} demeanor throughout all interactions
- Use a ${style?.toLowerCase()} communication approach
- ${customInstructions ? `Additional guidance: ${customInstructions}` : ''}

Key Behavioral Guidelines:
- Always greet customers warmly and introduce yourself
- Listen actively and ask clarifying questions when needed
- Provide clear, accurate information without using jargon
- Show empathy and understanding for customer concerns
- End conversations with clear next steps and contact information
- If you don't know something, admit it and offer to connect them with a specialist`;

    onBehaviorChange(enhancedPrompt);
  };

  React.useEffect(() => {
    if (activeTab === 'guided') {
      generateSmartPrompt();
    }
  }, [selectedTraits, selectedStyle, customInstructions, selectedTemplate]);

  React.useEffect(() => {
    if (selectedTemplate && !behavior) {
      onBehaviorChange(selectedTemplate.systemPrompt);
    }
  }, [selectedTemplate]);

  return (
    <div className="relative z-10 max-w-4xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Step 3: Configure Agent Behavior</h3>
        <p className="text-gray-300 mb-6">
          Define how your AI agent should behave and communicate with customers.
        </p>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="bg-gray-800 mb-6">
            <TabsTrigger value="guided" className="data-[state=active]:bg-gray-700">
              <Wand2 className="h-4 w-4 mr-2" />
              Guided Setup
            </TabsTrigger>
            <TabsTrigger value="advanced" className="data-[state=active]:bg-gray-700">
              <Settings className="h-4 w-4 mr-2" />
              Advanced
            </TabsTrigger>
            <TabsTrigger value="preview" className="data-[state=active]:bg-gray-700">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="guided" className="space-y-6">
            {selectedTemplate && (
              <Card className="bg-blue-900/20 border-blue-800">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <span className="text-xl mr-2">{selectedTemplate.icon}</span>
                    Using Template: {selectedTemplate.name}
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    We'll customize this template based on your preferences below.
                  </CardDescription>
                </CardHeader>
              </Card>
            )}

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Personality Traits</CardTitle>
                <CardDescription className="text-gray-300">
                  Choose the personality traits that best represent your brand
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {PERSONALITY_TRAITS.map((trait) => (
                    <TooltipProvider key={trait.id}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={selectedTraits.includes(trait.id) ? "default" : "outline"}
                            className={`h-auto p-3 text-left ${
                              selectedTraits.includes(trait.id)
                                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                                : 'bg-gray-800 border-gray-700 text-white hover:bg-gray-700'
                            }`}
                            onClick={() => handleTraitToggle(trait.id)}
                          >
                            <div>
                              <div className="font-medium">{trait.label}</div>
                              <div className="text-xs opacity-80">{trait.description}</div>
                            </div>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{trait.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Communication Style</CardTitle>
                <CardDescription className="text-gray-300">
                  How should your agent structure conversations?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {COMMUNICATION_STYLES.map((style) => (
                    <Button
                      key={style.id}
                      variant={selectedStyle === style.id ? "default" : "outline"}
                      className={`h-auto p-4 text-left ${
                        selectedStyle === style.id
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-gray-800 border-gray-700 text-white hover:bg-gray-700'
                      }`}
                      onClick={() => setSelectedStyle(style.id)}
                    >
                      <div>
                        <div className="font-medium">{style.label}</div>
                        <div className="text-xs opacity-80">{style.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Additional Instructions</CardTitle>
                <CardDescription className="text-gray-300">
                  Any specific guidelines or requirements for your agent? (Optional)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="e.g., Always mention our 24/7 customer service line, Use simple language for elderly customers, etc."
                  value={customInstructions}
                  onChange={(e) => setCustomInstructions(e.target.value)}
                  className="bg-gray-800 border-gray-700 text-white min-h-[100px]"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Advanced System Prompt
                </CardTitle>
                <CardDescription className="text-gray-300">
                  For experienced users: directly edit the system prompt that controls your agent's behavior.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={behavior}
                  onChange={(e) => onBehaviorChange(e.target.value)}
                  className="bg-gray-800 border-gray-700 text-white min-h-[300px] font-mono text-sm"
                  placeholder="Enter your custom system prompt here..."
                />
                <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-800 rounded-md">
                  <div className="flex items-start">
                    <Lightbulb className="h-4 w-4 text-yellow-400 mr-2 mt-0.5" />
                    <div className="text-sm text-yellow-200">
                      <strong>Tip:</strong> A good system prompt should define the agent's role, personality, 
                      key responsibilities, and communication guidelines. Be specific about how the agent 
                      should handle different scenarios.
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Behavior Preview</CardTitle>
                <CardDescription className="text-gray-300">
                  This is how your agent will be configured to behave
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-800 p-4 rounded-md border border-gray-700">
                  <pre className="text-sm text-gray-300 whitespace-pre-wrap font-mono">
                    {behavior || 'No behavior configured yet.'}
                  </pre>
                </div>
                
                {selectedTemplate && (
                  <div className="mt-4">
                    <h4 className="text-white font-medium mb-2">Template Features Included:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedTemplate.features.map((feature) => (
                        <Badge key={feature} variant="secondary" className="bg-blue-100 text-blue-800">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={onNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
          disabled={!behavior.trim()}
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
