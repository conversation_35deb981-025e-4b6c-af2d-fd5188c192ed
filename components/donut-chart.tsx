"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Re<PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts"

interface DonutChartProps {
  data: Array<{
    label: string
    value: number
  }>
}

export function DonutChart({ data }: DonutChartProps) {
  const COLORS = ["#2563EB"]
  const total = data.reduce((sum, item) => sum + item.value, 0)

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
          fill="#8884d8"
          paddingAngle={0}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Legend />
        <text x="50%" y="50%" textAnchor="middle" dominantBaseline="middle" className="text-2xl font-semibold">
          {total}
        </text>
      </PieChart>
    </ResponsiveContainer>
  )
}

