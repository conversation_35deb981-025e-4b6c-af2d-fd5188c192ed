"use client"

import { usePathname } from "next/navigation"
import Sidebar from "@/components/sidebar"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  
  // Routes that should use full-screen layout (no main sidebar)
  const fullScreenRoutes = [
    '/agent-builder/create'
  ]
  
  const isFullScreen = fullScreenRoutes.some(route => pathname.startsWith(route))
  
  if (isFullScreen) {
    // Full-screen layout for wizard flows
    return <>{children}</>
  }
  
  // Standard layout with sidebar
  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="flex-1 overflow-auto">{children}</main>
    </div>
  )
}
