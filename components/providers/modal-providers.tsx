"use client";

import { useState } from 'react';
import AgentBuilderModalProvider from '@/components/agent-builder/AgentBuilderModalProvider';

export default function ModalProviders() {
  const [isAgentBuilderOpen, setIsAgentBuilderOpen] = useState(false);

  return (
    <AgentBuilderModalProvider
      isOpen={isAgentBuilderOpen}
      onClose={() => setIsAgentBuilderOpen(false)}
    >
      <></>
    </AgentBuilderModalProvider>
  );
} 