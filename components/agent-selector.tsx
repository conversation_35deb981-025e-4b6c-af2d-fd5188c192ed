"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAgent } from "@/contexts/AgentContext"
import { Loader2 } from "lucide-react"

interface AgentSelectorProps {
  className?: string
}

export function AgentSelector({ className = "w-[200px]" }: AgentSelectorProps) {
  const { selectedAgent, setSelectedAgent, availableAgents, getAgentLabel, isLoading } = useAgent()

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="ml-2 text-sm text-muted-foreground">Loading agents...</span>
      </div>
    )
  }

  return (
    <Select value={selectedAgent} onValueChange={setSelectedAgent}>
      <SelectTrigger className={className}>
        <SelectValue>{getAgentLabel(selectedAgent)}</SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Agents</SelectItem>
        {availableAgents.map(agent => (
          <SelectItem key={agent} value={agent}>{agent}</SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
