"use client"

import { useState, use<PERSON>ffe<PERSON>, useCallback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Area, AreaChart, Bar, BarChart, ResponsiveContainer, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>onte<PERSON>, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Info, AlertCircle, RefreshCcw } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { TimeframeSelector } from "@/components/timeframe-selector"
import { AgentSelector } from "@/components/agent-selector"
import { useTimeframe } from "@/contexts/TimeframeContext"
import { useAgent } from "@/contexts/AgentContext"

// Removed TIMEFRAME_OPTIONS - using global timeframe context

// Interface for analytics data from API
interface AnalyticsData {
  id: string;
  total_minutes: number;
  total_cost: number;
  day: string;
  total_calls: number;
  sentiments: {
    Positive: number;
    Neutral: number;
    Negative: number;
  };
}

// Interface for metrics calculations
interface PeriodMetrics {
  totalMinutes: number;
  totalCalls: number;
  totalCost: number;
  averageCost: number;
}

// Interface for metrics state
interface Metrics {
  totalMinutes: number;
  minutesChange: number;
  totalCalls: number;
  callsChange: number;
  totalCost: number;
  costChange: number;
  averageCost: number;
  averageCostChange: number;
}

// Removed TimeframeOption type - using global timeframe context

interface ChartData {
  minutes: Array<{ date: string; latest: number; previous: number }>;
  calls: Array<{ date: string; latest: number; previous: number }>;
  cost: Array<{ date: string; latest: number; previous: number }>;
  avgCost: Array<{ date: string; latest: number; previous: number }>;
  sentiment: Array<{
    date: string;
    Positive: number;
    Neutral: number;
    Negative: number;
  }>;
}

// Define a type for the bar click event
interface BarClickEvent {
  activePayload?: Array<{
    payload: {
      date: string;
      [key: string]: unknown;
    };
  }>;
}

export default function Analytics() {
  const router = useRouter()
  const { timeframe } = useTimeframe()
  const { selectedAgent } = useAgent()
  const [metrics, setMetrics] = useState<Metrics>({
    totalMinutes: 0,
    minutesChange: 0,
    totalCalls: 0,
    callsChange: 0,
    totalCost: 0,
    costChange: 0,
    averageCost: 0,
    averageCostChange: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [chartData, setChartData] = useState<ChartData>({
    minutes: [],
    calls: [],
    cost: [],
    avgCost: [],
    sentiment: []
  })

  // Removed localStorage effects - using global timeframe state

  const fetchMetrics = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({ timeframe })
      if (selectedAgent !== "all") {
        params.append("agent", selectedAgent)
      }
      const response = await fetch(`/api/analytics?${params.toString()}`)

      // Log response details for debugging - fixed headers iteration
      console.log('Analytics API response:', {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type'),
      });

      if (!response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          console.error('API error response:', errorData);
          throw new Error(errorData.error || `Failed to fetch analytics data: ${response.status} ${response.statusText}`);
        } else {
          // Handle non-JSON responses (like HTML error pages)
          const text = await response.text();
          console.error('Non-JSON error response:', text.substring(0, 500)); // Log first 500 chars
          throw new Error(`Server returned non-JSON response: ${response.status} ${response.statusText}`);
        }
      }

      const data = await response.json();
      console.log('Analytics data received:', data);

      const { current: currentData, previous: previousData } = data;

      if (!currentData || !previousData) {
        throw new Error('No data received from the server');
      }

      // Calculate current period metrics
      const currentMetrics: PeriodMetrics = {
        totalMinutes: currentData.reduce((sum: number, day: AnalyticsData) => sum + day.total_minutes, 0),
        totalCalls: currentData.reduce((sum: number, day: AnalyticsData) => sum + day.total_calls, 0),
        totalCost: currentData.reduce((sum: number, day: AnalyticsData) => sum + day.total_cost, 0),
        averageCost: 0
      }
      currentMetrics.averageCost = currentMetrics.totalCalls > 0
        ? currentMetrics.totalCost / currentMetrics.totalCalls
        : 0

      // Calculate previous period metrics
      const previousMetrics: PeriodMetrics = {
        totalMinutes: previousData.reduce((sum: number, day: AnalyticsData) => sum + day.total_minutes, 0),
        totalCalls: previousData.reduce((sum: number, day: AnalyticsData) => sum + day.total_calls, 0),
        totalCost: previousData.reduce((sum: number, day: AnalyticsData) => sum + day.total_cost, 0),
        averageCost: 0
      }
      previousMetrics.averageCost = previousMetrics.totalCalls > 0
        ? previousMetrics.totalCost / previousMetrics.totalCalls
        : 0

      // Calculate percentage changes
      const calculateChange = (current: number, previous: number) =>
        previous === 0 ? 0 : ((current - previous) / previous) * 100

      setMetrics({
        totalMinutes: Number(currentMetrics.totalMinutes.toFixed(2)),
        minutesChange: calculateChange(currentMetrics.totalMinutes, previousMetrics.totalMinutes),
        totalCalls: currentMetrics.totalCalls,
        callsChange: calculateChange(currentMetrics.totalCalls, previousMetrics.totalCalls),
        totalCost: Number(currentMetrics.totalCost.toFixed(3)),
        costChange: calculateChange(currentMetrics.totalCost, previousMetrics.totalCost),
        averageCost: Number(currentMetrics.averageCost.toFixed(3)),
        averageCostChange: calculateChange(currentMetrics.averageCost, previousMetrics.averageCost)
      })

      // Create chart data
      const allDays = Array.from(new Set([
        ...currentData.map((d: AnalyticsData) => d.day),
        ...previousData.map((d: AnalyticsData) => d.day)
      ])).sort()

      // Fill in missing days between the first and last day
      const firstDay = new Date(allDays[0])
      const lastDay = new Date(allDays[allDays.length - 1])
      const completeDays: string[] = []

      for (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 1)) {
        completeDays.push(d.toISOString().split('T')[0])
      }

      const createChartData = (metric: keyof AnalyticsData) =>
        completeDays.map(day => ({
          date: day,
          latest: currentData.find((d: AnalyticsData) => d.day === day)?.[metric] || 0,
          previous: previousData.find((d: AnalyticsData) => d.day === day)?.[metric] || 0
        }))

      setChartData({
        minutes: createChartData('total_minutes'),
        calls: createChartData('total_calls'),
        cost: createChartData('total_cost'),
        avgCost: completeDays.map(day => {
          const currentDay = currentData.find((d: AnalyticsData) => d.day === day)
          const previousDay = previousData.find((d: AnalyticsData) => d.day === day)
          return {
            date: day,
            latest: currentDay ? (currentDay.total_calls > 0 ? currentDay.total_cost / currentDay.total_calls : 0) : 0,
            previous: previousDay ? (previousDay.total_calls > 0 ? previousDay.total_cost / previousDay.total_calls : 0) : 0
          }
        }),
        sentiment: completeDays.map(day => {
          const dayData = currentData.find((d: AnalyticsData) => d.day === day)
          return {
            date: day,
            Positive: dayData?.sentiments.Positive || 0,
            Neutral: dayData?.sentiments.Neutral || 0,
            Negative: dayData?.sentiments.Negative || 0
          }
        })
      })

    } catch (error) {
      console.error('Error fetching metrics:', error)
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
      setMetrics({
        totalMinutes: 0,
        minutesChange: 0,
        totalCalls: 0,
        callsChange: 0,
        totalCost: 0,
        costChange: 0,
        averageCost: 0,
        averageCostChange: 0
      })
      setChartData({
        minutes: [],
        calls: [],
        cost: [],
        avgCost: [],
        sentiment: []
      })
    } finally {
      setLoading(false)
    }
  }, [timeframe, selectedAgent])

  useEffect(() => {
    fetchMetrics()
  }, [timeframe, selectedAgent, fetchMetrics])

  // Removed handleTimeframeChange - using global timeframe state

  const handleBarClick = (data: BarClickEvent) => {
    if (data && data.activePayload && data.activePayload[0]) {
      const date = data.activePayload[0].payload.date
      // Navigate to calls page with the date filter
      router.push(`/calls?date=${date}`)
    }
  }

  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Analytics</h1>
        <div className="flex items-center gap-4">
          {error && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchMetrics()}
              className="text-red-500 hover:text-red-600"
            >
              <RefreshCcw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
          <AgentSelector />
          <TimeframeSelector />
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}. Data shown may be outdated.
          </AlertDescription>
        </Alert>
      )}

      {/* Metric Cards */}
      <div className="grid grid-cols-4 gap-4">
        {loading ? (
          // Loading skeleton
          <>
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                  <div className="h-4 w-12 bg-muted animate-pulse rounded" />
                </CardHeader>
                <CardContent>
                  <div className="h-8 w-16 bg-muted animate-pulse rounded" />
                </CardContent>
              </Card>
            ))}
          </>
        ) : (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm font-medium">Total Call Minutes</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Total duration of all calls in minutes.</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Calculation: Sum of all call durations converted from milliseconds to minutes.
                          {timeframe === "today"
                            ? " Comparing today with yesterday."
                            : ` Comparing last ${timeframe} days with previous ${timeframe} days.`}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <span className={`text-xs ${metrics.minutesChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {metrics.minutesChange >= 0 ? '+' : ''}{metrics.minutesChange.toFixed(1)}%
                </span>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalMinutes.toFixed(2)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm font-medium">Number of Calls</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Total number of calls made in the selected period.</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Calculation: Count of all calls.
                          {timeframe === "today"
                            ? " Comparing today with yesterday."
                            : ` Comparing last ${timeframe} days with previous ${timeframe} days.`}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <span className={`text-xs ${metrics.callsChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {metrics.callsChange >= 0 ? '+' : ''}{metrics.callsChange.toFixed(1)}%
                </span>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalCalls}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Total cost of all calls in the selected period.</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Calculation: Sum of all call costs in dollars.
                          {timeframe === "today"
                            ? " Comparing today with yesterday."
                            : ` Comparing last ${timeframe} days with previous ${timeframe} days.`}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <span className={`text-xs ${metrics.costChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {metrics.costChange >= 0 ? '+' : ''}{metrics.costChange.toFixed(1)}%
                </span>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${(metrics.totalCost / 100).toFixed(2)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm font-medium">Average Cost per Call</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Average cost per call in the selected period.</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Calculation: Total cost divided by number of calls (in dollars).
                          {timeframe === "today"
                            ? " Comparing today with yesterday."
                            : ` Comparing last ${timeframe} days with previous ${timeframe} days.`}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <span className={`text-xs ${metrics.averageCostChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {metrics.averageCostChange >= 0 ? '+' : ''}{metrics.averageCostChange.toFixed(1)}%
                </span>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${(metrics.averageCost / 100).toFixed(2)}</div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-2 gap-4">
        {/* Total Call Minutes Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Total Call Minutes</CardTitle>
            <span className="text-xs text-green-500">+18.7%</span>
            <div className="text-xs text-muted-foreground">The total number of minutes spent on calls each day</div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData.minutes}>
                <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <RechartsTooltip />
                <Area type="monotone" dataKey="latest" stroke="#10b981" fill="#10b981" fillOpacity={0.2} />
                <Area type="monotone" dataKey="previous" stroke="#6b7280" fill="#6b7280" fillOpacity={0.1} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Number of Calls Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Number of Calls</CardTitle>
            <span className="text-xs text-red-500">-30.0%</span>
            <div className="text-xs text-muted-foreground">The total number of calls made each day</div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData.calls}>
                <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <RechartsTooltip />
                <Area type="monotone" dataKey="latest" stroke="#f59e0b" fill="#f59e0b" fillOpacity={0.2} />
                <Area type="monotone" dataKey="previous" stroke="#6b7280" fill="#6b7280" fillOpacity={0.1} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Total Cost Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <span className="text-xs text-green-500">+22.2%</span>
            <div className="text-xs text-muted-foreground">The total cost of calls made each day</div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData.cost}>
                <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <RechartsTooltip />
                <Area type="monotone" dataKey="latest" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.2} />
                <Area type="monotone" dataKey="previous" stroke="#6b7280" fill="#6b7280" fillOpacity={0.1} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Average Cost per Call Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Average Cost per Call</CardTitle>
            <span className="text-xs text-green-500">+74.6%</span>
            <div className="text-xs text-muted-foreground">The average cost of calls made each day</div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData.avgCost}>
                <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <RechartsTooltip />
                <Area type="monotone" dataKey="latest" stroke="#ef4444" fill="#ef4444" fillOpacity={0.2} />
                <Area type="monotone" dataKey="previous" stroke="#6b7280" fill="#6b7280" fillOpacity={0.1} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* User Sentiment Chart */}
        <Card className="col-span-2">
          <CardHeader>
            <CardTitle className="text-sm font-medium">User Sentiment Trends</CardTitle>
            <div className="text-xs text-muted-foreground">Distribution of user sentiments by day (click bars to view calls)</div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={chartData.sentiment}
                onClick={handleBarClick}
                style={{ cursor: 'pointer' }}
              >
                <XAxis
                  dataKey="date"
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <Legend
                  verticalAlign="top"
                  height={36}
                  iconType="circle"
                  formatter={(value) => <span className="text-sm">{value}</span>}
                />
                <RechartsTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-background border rounded-lg shadow-lg p-2">
                          <p className="text-sm font-medium">
                            {new Date(payload[0].payload.date).toLocaleDateString()}
                          </p>
                          {payload.map((entry) => (
                            <div key={entry.name} className="flex items-center gap-2">
                              <div
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: entry.color }}
                              />
                              <span className="text-sm">{entry.name}:</span>
                              <span className="text-sm font-medium">{entry.value}</span>
                            </div>
                          ))}
                          <p className="text-xs text-muted-foreground mt-2">Click to view calls</p>
                        </div>
                      )
                    }
                    return null
                  }}
                />
                <Bar
                  dataKey="Positive"
                  stackId="sentiment"
                  fill="#10b981"
                  name="Positive"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="Neutral"
                  stackId="sentiment"
                  fill="#fef08a"
                  name="Neutral"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="Negative"
                  stackId="sentiment"
                  fill="#ef4444"
                  name="Negative"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}