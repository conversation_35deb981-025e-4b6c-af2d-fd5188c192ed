# RetellAI Agent Builder Documentation

## Overview

This document outlines the process of building AI voice agents using RetellAI's SDK and APIs within the VALabs Agent Builder platform. RetellAI provides powerful tools for creating, customizing, and deploying voice AI agents that can handle complex insurance-related conversations.

## SDK Installation

### Node.js SDK

```bash
npm install retell-sdk
```

### Basic SDK Configuration

```typescript
import Retell from 'retell-sdk';

const client = new Retell({
  apiKey: process.env.RETELL_API_KEY,
});
```

## Agent Creation Process

### 1. Creating an Agent

Agents are the core entities that handle voice conversations. Creating an agent requires specifying a voice and a response engine.

```typescript
async function createAgent(name: string, description: string) {
  const agentResponse = await client.agent.create({
    name: name,
    metadata: { description: description },
    response_engine: { 
      type: 'retell-llm',
      llm_id: process.env.RETELL_LLM_ID 
    },
    voice_id: '11labs-Adrian', // Can be customized
  });
  
  return agentResponse.agent_id;
}
```

### 2. Configuring Agent Behavior

Agents can be customized with specific instructions and knowledge bases to handle insurance-specific scenarios.

```typescript
async function configureAgent(agentId: string, instructions: string) {
  await client.agent.update(agentId, {
    llm_webhook_config: {
      instructions: instructions,
      temperature: 0.7,
      system_prompt_template: `You are an AI assistant for ${process.env.COMPANY_NAME}, 
      an insurance company. Your role is to help insurance agents with their daily tasks.
      ${instructions}`
    }
  });
}
```

### 3. Adding Knowledge Bases

Knowledge bases provide agents with domain-specific information about insurance policies, procedures, and regulations.

```typescript
async function addKnowledgeBase(agentId: string, documents: Array<{name: string, content: string}>) {
  // First create a knowledge base
  const knowledgeBase = await client.knowledgeBase.create({
    name: "Insurance Policies",
    description: "Contains information about insurance policies and procedures"
  });
  
  // Then add documents to the knowledge base
  for (const doc of documents) {
    await client.knowledgeBase.addDocument(knowledgeBase.id, {
      name: doc.name,
      content: doc.content
    });
  }
  
  // Finally, attach the knowledge base to the agent
  await client.agent.update(agentId, {
    knowledge_base_ids: [knowledgeBase.id]
  });
  
  return knowledgeBase.id;
}
```

### 4. Training the Agent

Training involves fine-tuning the agent with example conversations and scenarios specific to insurance.

```typescript
async function trainAgent(agentId: string, trainingExamples: Array<{user: string, agent: string}>) {
  await client.agent.train(agentId, {
    training_examples: trainingExamples
  });
}
```

### 5. Testing the Agent

Before deployment, agents should be tested to ensure they handle conversations appropriately.

```typescript
async function testAgent(agentId: string, testScenario: string) {
  const testResult = await client.agent.test(agentId, {
    input: testScenario
  });
  
  return testResult.response;
}
```

## Call Management

### 1. Initiating Calls

Agents can initiate outbound calls to clients or receive inbound calls.

```typescript
async function initiateCall(agentId: string, phoneNumber: string) {
  const call = await client.call.create({
    agent_id: agentId,
    customer_number: phoneNumber,
    agent_number: process.env.AGENT_PHONE_NUMBER
  });
  
  return call.call_id;
}
```

### 2. Monitoring Active Calls

Track ongoing calls and their status.

```typescript
async function getActiveCall(callId: string) {
  const callStatus = await client.call.retrieve(callId);
  return callStatus;
}
```

### 3. Call Analytics

After calls are completed, retrieve analytics to improve agent performance.

```typescript
async function getCallAnalytics(callId: string) {
  const analytics = await client.call.getAnalytics(callId);
  return analytics;
}
```

## Advanced Features

### 1. Custom Voice Selection

RetellAI offers various voice options that can be selected based on preference.

```typescript
async function listAvailableVoices() {
  const voices = await client.voice.list();
  return voices;
}
```

### 2. Real-time Call Intervention

Allow human operators to intervene in ongoing calls if necessary.

```typescript
async function interventCall(callId: string, message: string) {
  await client.call.intervene(callId, {
    message: message
  });
}
```

### 3. Multi-agent Conversations

Create scenarios where multiple agents interact with each other or with a customer.

```typescript
async function createMultiAgentCall(agentIds: string[], customerNumber: string) {
  const call = await client.call.createMultiAgent({
    agent_ids: agentIds,
    customer_number: customerNumber
  });
  
  return call.call_id;
}
```

## Integration with VALabs Platform

### 1. Agent Dashboard

Create a dashboard to monitor all agents, their performance, and call history.

```typescript
async function getAgentDashboard() {
  const agents = await client.agent.list();
  const callHistory = await client.call.list();
  
  return {
    agents: agents,
    calls: callHistory
  };
}
```

### 2. User Management

Manage access to different agents based on user roles.

```typescript
async function assignAgentToUser(agentId: string, userId: string) {
  // This would be implemented through your application's user management system
  // and would involve updating your database records
}
```

## Best Practices

1. **Security**: Store API keys securely in environment variables
2. **Error Handling**: Implement robust error handling for all API calls
3. **Rate Limiting**: Be aware of API rate limits and implement appropriate throttling
4. **Testing**: Thoroughly test agents in controlled environments before deployment
5. **Monitoring**: Set up monitoring for agent performance and call quality
6. **Compliance**: Ensure all agents comply with relevant regulations regarding AI, voice calls, and insurance

## Troubleshooting

### Common Issues

1. **Agent Not Responding**: Check if the LLM configuration is correct
2. **Poor Voice Quality**: Verify the selected voice model and network conditions
3. **Knowledge Base Issues**: Ensure documents are properly formatted and indexed
4. **Call Connection Problems**: Check phone number formats and telephony provider settings

### Support Resources

- RetellAI Documentation: [https://docs.retellai.com](https://docs.retellai.com)
- RetellAI GitHub: [https://github.com/RetellAI](https://github.com/RetellAI)
- RetellAI Support: <EMAIL> 