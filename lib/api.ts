import { useState, useEffect, useCallback } from 'react'
import type { AnalyticsData } from '@/types/analytics'

export function useAnalytics(dateRange: string) {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/analytics?timeframe=${dateRange}`)
      const data = await response.json()
      setData(data)
      setError(null)
    } catch (err) {
      setError(err as Error)
    } finally {
      setIsLoading(false)
    }
  }, [dateRange])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, isLoading, error, fetchData }
} 