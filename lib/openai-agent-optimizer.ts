import { AgentTemplate } from './agent-templates';

// Interface for the complete agent data collected throughout the wizard
export interface AgentWizardData {
  // Template Selection
  selectedTemplate: AgentTemplate | null;
  
  // Basic Info
  name: string;
  description: string;
  
  // Voice Selection
  voiceId: string;
  voiceName?: string;
  voiceProvider?: string;
  voiceGender?: string;
  voiceAccent?: string;
  
  // Behavior Configuration
  behavior: string;
  personalityTraits?: string[];
  communicationStyle?: string;
  customInstructions?: string;
  
  // Knowledge Base
  selectedKnowledgeBases?: string[];
  knowledgeBaseTopics?: string[];
  
  // Training Examples
  trainingExamples?: Array<{
    scenario: string;
    userInput: string;
    expectedResponse: string;
  }>;
  
  // Additional context
  organizationContext?: string;
  useCase?: string;
  targetAudience?: string;
}

// Interface for OpenAI optimized response
export interface OptimizedAgentConfig {
  // RetellAI LLM Configuration
  llm: {
    general_prompt: string;
    begin_message: string;
    model: string;
    model_temperature?: number;
    general_tools?: any[];
  };
  
  // RetellAI Agent Configuration
  agent: {
    agent_name: string;
    voice_id: string;
    language: string;
    response_engine: {
      type: string;
      llm_id: string; // Will be populated after LLM creation
    };
    webhook_url?: string;
  };
  
  // Additional metadata
  metadata: {
    optimization_notes: string;
    suggested_improvements: string[];
    confidence_score: number;
  };
}

// Pre-prompt template for OpenAI
export const AGENT_OPTIMIZATION_PROMPT = `You are an expert AI agent configuration specialist for voice-based customer service systems using RetellAI. Your task is to analyze the provided agent wizard data and generate an optimized configuration that will create the most effective voice agent possible.

## Your Expertise:
- Voice AI conversation design and flow optimization
- Customer service best practices and psychology
- RetellAI platform capabilities and limitations
- Insurance industry knowledge and compliance requirements
- Natural language processing and conversation design

## Input Data Analysis:
You will receive comprehensive data from a 7-step agent builder wizard including:
1. Selected template with pre-configured prompts and examples
2. Basic agent information (name, description, purpose)
3. Voice characteristics and selection
4. Behavior configuration and personality traits
5. Knowledge base topics and information sources
6. Training examples and conversation scenarios
7. Organizational context and use case requirements

## Output Requirements:
Generate a JSON response with the following structure:

\`\`\`json
{
  "llm": {
    "general_prompt": "Comprehensive system prompt optimized for voice conversations",
    "begin_message": "Natural, engaging opening message",
    "model": "gpt-4o-mini",
    "model_temperature": 0.7,
    "general_tools": []
  },
  "agent": {
    "agent_name": "Optimized agent name",
    "voice_id": "selected_voice_id",
    "language": "en-US"
  },
  "metadata": {
    "optimization_notes": "Explanation of key optimizations made",
    "suggested_improvements": ["List of additional recommendations"],
    "confidence_score": 0.95
  }
}
\`\`\`

## Optimization Guidelines:

### Voice Conversation Optimization:
- Design prompts specifically for voice interactions (shorter responses, natural speech patterns)
- Include conversation flow management and turn-taking cues
- Add empathy and emotional intelligence elements
- Optimize for clarity and comprehension in audio format

### Industry-Specific Enhancements:
- Incorporate insurance industry terminology and compliance requirements
- Add appropriate disclaimers and legal language where needed
- Include escalation procedures for complex issues
- Ensure HIPAA/privacy compliance considerations

### Personality and Tone Optimization:
- Blend template characteristics with custom personality traits
- Ensure consistency between voice selection and personality
- Optimize communication style for target audience
- Balance professionalism with approachability

### Knowledge Integration:
- Seamlessly integrate knowledge base topics into conversation flow
- Create natural transitions between different information areas
- Add appropriate "I don't know" responses with escalation paths
- Include verification and confirmation procedures

### Training Example Enhancement:
- Expand on provided training examples with variations
- Add edge case handling and error recovery
- Include conversation repair strategies
- Optimize response timing and pacing

## Critical Requirements:
1. Maintain all essential information from the original template
2. Preserve the selected voice ID exactly as provided
3. Ensure the agent name reflects the intended purpose and branding
4. Keep responses concise but comprehensive for voice delivery
5. Include appropriate error handling and escalation procedures
6. Maintain professional insurance industry standards
7. Ensure compliance with relevant regulations and privacy requirements

## Response Format:
- Return ONLY valid JSON - no additional text or formatting
- Ensure all required fields are populated
- Provide detailed optimization notes explaining key decisions
- Include actionable suggestions for further improvements
- Assign a confidence score based on data completeness and optimization potential

Now, analyze the following agent wizard data and provide your optimized configuration:`;

// Function to bundle wizard data for OpenAI
export function bundleAgentWizardData(
  formData: any,
  selectedTemplate: AgentTemplate | null,
  selectedVoice: any,
  additionalContext?: Partial<AgentWizardData>
): AgentWizardData {
  return {
    selectedTemplate,
    name: formData.name || '',
    description: formData.description || '',
    voiceId: formData.voiceId || selectedVoice?.voice_id || '',
    voiceName: selectedVoice?.voice_name,
    voiceProvider: selectedVoice?.provider,
    voiceGender: selectedVoice?.gender,
    voiceAccent: selectedVoice?.accent,
    behavior: formData.behavior || selectedTemplate?.systemPrompt || '',
    personalityTraits: additionalContext?.personalityTraits || [],
    communicationStyle: additionalContext?.communicationStyle,
    customInstructions: additionalContext?.customInstructions,
    selectedKnowledgeBases: additionalContext?.selectedKnowledgeBases || [],
    knowledgeBaseTopics: selectedTemplate?.knowledgeBaseTopics || [],
    trainingExamples: selectedTemplate?.trainingExamples || [],
    organizationContext: additionalContext?.organizationContext,
    useCase: additionalContext?.useCase,
    targetAudience: additionalContext?.targetAudience,
  };
}

// Function to validate OpenAI response
export function validateOptimizedConfig(config: any): config is OptimizedAgentConfig {
  return (
    config &&
    typeof config === 'object' &&
    config.llm &&
    typeof config.llm.general_prompt === 'string' &&
    typeof config.llm.begin_message === 'string' &&
    config.agent &&
    typeof config.agent.agent_name === 'string' &&
    typeof config.agent.voice_id === 'string' &&
    config.metadata &&
    typeof config.metadata.optimization_notes === 'string' &&
    Array.isArray(config.metadata.suggested_improvements) &&
    typeof config.metadata.confidence_score === 'number'
  );
}

// Function to create OpenAI payload
export function createOpenAIPayload(wizardData: AgentWizardData) {
  return {
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content: AGENT_OPTIMIZATION_PROMPT
      },
      {
        role: "user",
        content: JSON.stringify(wizardData, null, 2)
      }
    ],
    temperature: 0.3, // Lower temperature for more consistent, structured output
    max_tokens: 4000,
    response_format: { type: "json_object" }
  };
}
