import { Retell } from 'retell-sdk';

// Only initialize on server side
let retell: Retell | null = null;

if (typeof window === 'undefined' && process.env.RETELL_API_KEY) {
  retell = new Retell({
    apiKey: process.env.RETELL_API_KEY,
  });
}

// Helper function to ensure we have a client
function getRetellClient() {
  if (!retell) {
    throw new Error('RetellAI client not initialized. This function should only be called server-side.');
  }
  return retell;
}

// Type-safe wrapper functions for common operations
export async function listCalls(params?: {
  start_timestamp?: string;
  end_timestamp?: string;
  limit?: number;
}) {
  const client = getRetellClient();
  return client.call.list(params || {});
}

export async function getCallDetails(callId: string) {
  const client = getRetellClient();
  return client.call.retrieve(callId);
}

export async function listAgents() {
  const client = getRetellClient();
  return client.agent.list();
}

export async function getAgentDetails(agentId: string) {
  const client = getRetellClient();
  return client.agent.retrieve(agentId);
}

export async function listKnowledgeBases() {
  const client = getRetellClient();
  return client.knowledgeBase.list();
}

export async function getKnowledgeBase(knowledgeBaseId: string) {
  const client = getRetellClient();
  return client.knowledgeBase.retrieve(knowledgeBaseId);
}

export async function addKnowledgeBaseSources(
  knowledgeBaseId: string,
  files: Array<{
    type: 'document' | 'url';
    file_url?: string;
    url?: string;
  }>
) {
  const client = getRetellClient();
  return client.knowledgeBase.addSources(knowledgeBaseId, { files });
}

export async function deleteKnowledgeBaseSource(
  knowledgeBaseId: string,
  sourceId: string
) {
  const client = getRetellClient();
  return client.knowledgeBase.deleteSource(sourceId);
}

// Phone number management
export async function listPhoneNumbers() {
  const client = getRetellClient();
  return client.phoneNumber.list();
}

export async function createPhoneNumber(areaCode: number, agentId?: string) {
  const client = getRetellClient();
  return client.phoneNumber.create({
    area_code: areaCode,
    inbound_agent_id: agentId,
    outbound_agent_id: agentId,
  });
}

export async function updatePhoneNumber(
  phoneNumber: string,
  updates: {
    inbound_agent_id?: string;
    outbound_agent_id?: string;
    nickname?: string;
  }
) {
  const client = getRetellClient();
  return client.phoneNumber.update(phoneNumber, updates);
}

export async function deletePhoneNumber(phoneNumber: string) {
  const client = getRetellClient();
  return client.phoneNumber.delete(phoneNumber);
}

// Voice management
export async function listVoices() {
  const client = getRetellClient();
  return client.voice.list();
}

export async function getVoice(voiceId: string) {
  const client = getRetellClient();
  return client.voice.retrieve(voiceId);
}

// Call management
export async function createPhoneCall(params: {
  from_number: string;
  to_number: string;
  override_agent_id?: string;
  retell_llm_dynamic_variables?: Record<string, string>;
  metadata?: Record<string, string>;
}) {
  const client = getRetellClient();
  return client.call.createPhoneCall(params);
}

export async function createWebCall(params: {
  agent_id: string;
  retell_llm_dynamic_variables?: Record<string, string>;
  metadata?: Record<string, string>;
}) {
  const client = getRetellClient();
  return client.call.createWebCall(params);
}

// Agent management
export async function createAgent(params: {
  agent_name?: string;
  voice_id: string;
  response_engine: {
    type: 'retell-llm';
    llm_id: string;
  };
  language?: string;
  webhook_url?: string;
}) {
  const client = getRetellClient();
  return client.agent.create(params);
}

export async function updateAgent(agentId: string, updates: any) {
  const client = getRetellClient();
  return client.agent.update(agentId, updates);
}

export async function deleteAgent(agentId: string) {
  const client = getRetellClient();
  return client.agent.delete(agentId);
}

// LLM management
export async function listRetellLLMs() {
  const client = getRetellClient();
  return client.llm.list();
}

export async function createRetellLLM(params: {
  general_prompt: string;
  begin_message?: string;
  general_tools?: any[];
  model?: string;
}) {
  const client = getRetellClient();
  return client.llm.create(params);
}

export async function updateRetellLLM(llmId: string, updates: any) {
  const client = getRetellClient();
  return client.llm.update(llmId, updates);
}

export async function deleteRetellLLM(llmId: string) {
  const client = getRetellClient();
  return client.llm.delete(llmId);
}