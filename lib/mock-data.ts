export const mockAnalyticsData = {
  totalMinutes: 7.11,
  minutesChange: 18.7,
  totalCalls: 14,
  callsChange: -30.0,
  totalCost: 0.97,
  costChange: 22.2,
  averageCost: 0.07,
  averageCostChange: 74.6,
  charts: {
    callMinutes: [
      { date: "Jan 23", latest: 0.8, previous: 1.0 },
      { date: "Jan 24", latest: 1.2, previous: 0.9 },
      { date: "Jan 25", latest: 0.9, previous: 1.1 },
      { date: "Jan 26", latest: 1.5, previous: 0.8 },
      { date: "Jan 27", latest: 4.5, previous: 2.0 },
      { date: "Jan 28", latest: 2.5, previous: 3.0 },
      { date: "Jan 29", latest: 1.5, previous: 1.2 },
    ],
    callVolume: [
      { date: "Jan 23", latest: 6, previous: 4 },
      { date: "Jan 24", latest: 8, previous: 5 },
      { date: "Jan 25", latest: 10, previous: 6 },
      { date: "Jan 26", latest: 7, previous: 8 },
      { date: "Jan 27", latest: 12, previous: 9 },
      { date: "Jan 28", latest: 9, previous: 7 },
      { date: "Jan 29", latest: 5, previous: 4 },
    ],
    totalCost: [
      { date: "Jan 23", latest: 0.12, previous: 0.10 },
      { date: "Jan 24", latest: 0.15, previous: 0.12 },
      { date: "Jan 25", latest: 0.18, previous: 0.15 },
      { date: "Jan 26", latest: 0.25, previous: 0.20 },
      { date: "Jan 27", latest: 0.35, previous: 0.25 },
      { date: "Jan 28", latest: 0.28, previous: 0.22 },
      { date: "Jan 29", latest: 0.15, previous: 0.12 },
    ],
    costPerCall: [
      { date: "Jan 23", latest: 0.02, previous: 0.025 },
      { date: "Jan 24", latest: 0.019, previous: 0.024 },
      { date: "Jan 25", latest: 0.018, previous: 0.025 },
      { date: "Jan 26", latest: 0.036, previous: 0.025 },
      { date: "Jan 27", latest: 0.029, previous: 0.028 },
      { date: "Jan 28", latest: 0.031, previous: 0.031 },
      { date: "Jan 29", latest: 0.03, previous: 0.03 },
    ],
  },
  callEndReasons: [
    { label: "user_hangup", value: 8 },
    { label: "completed", value: 4 },
    { label: "system_error", value: 1 },
    { label: "timeout", value: 1 },
  ]
} 