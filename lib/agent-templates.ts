export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: 'customer_service' | 'sales' | 'claims' | 'enrollment' | 'general';
  icon: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedSetupTime: string;
  features: string[];
  systemPrompt: string;
  beginMessage: string;
  suggestedVoices: string[];
  knowledgeBaseTopics: string[];
  trainingExamples: Array<{
    scenario: string;
    userInput: string;
    expectedResponse: string;
  }>;
  tools?: Array<{
    type: string;
    name: string;
    description: string;
    config?: any;
  }>;
  tags: string[];
  isPopular?: boolean;
  isRecommended?: boolean;
}

export const AGENT_TEMPLATES: AgentTemplate[] = [
  {
    id: 'customer-service-general',
    name: 'Customer Service Assistant',
    description: 'A friendly AI assistant that helps customers with general inquiries, policy questions, and basic support needs.',
    category: 'customer_service',
    icon: '🎧',
    difficulty: 'beginner',
    estimatedSetupTime: '5 minutes',
    features: [
      'Policy information lookup',
      'General customer support',
      'FAQ responses',
      'Call routing assistance'
    ],
    systemPrompt: `You are a helpful customer service representative for an insurance company. Your role is to:

1. Greet customers warmly and professionally
2. Listen carefully to their questions and concerns
3. Provide accurate information about policies and coverage
4. Guide customers to the right resources or departments
5. Maintain a friendly, patient, and empathetic tone

Key Guidelines:
- Always verify customer identity before discussing policy details
- If you don't know something, admit it and offer to connect them with a specialist
- Keep responses clear and jargon-free
- Show empathy for customer concerns
- End calls with next steps and contact information`,
    beginMessage: "Hello! Thank you for calling. I'm here to help you with any questions about your insurance coverage. How can I assist you today?",
    suggestedVoices: ['11labs-Rachel', '11labs-Sarah', 'openai-alloy'],
    knowledgeBaseTopics: [
      'General policy information',
      'Coverage types and benefits',
      'Claims process overview',
      'Contact information and hours'
    ],
    trainingExamples: [
      {
        scenario: 'Customer asking about coverage',
        userInput: "What does my policy cover?",
        expectedResponse: "I'd be happy to help you understand your coverage. To provide you with accurate information about your specific policy, I'll need to verify your identity first. Could you please provide your policy number or the last four digits of your Social Security number?"
      },
      {
        scenario: 'Customer wants to file a claim',
        userInput: "I need to file a claim",
        expectedResponse: "I can definitely help you get started with filing a claim. First, I want to make sure you're okay - is this regarding an accident or injury? Once I know more about your situation, I can guide you through the next steps and connect you with our claims department."
      }
    ],
    tags: ['customer-service', 'general', 'beginner-friendly'],
    isPopular: true,
    isRecommended: true
  },
  {
    id: 'enrollment-specialist',
    name: 'Benefits Enrollment Specialist',
    description: 'Specialized agent for guiding employees through benefits enrollment, explaining options, and helping with plan selection.',
    category: 'enrollment',
    icon: '📋',
    difficulty: 'intermediate',
    estimatedSetupTime: '10 minutes',
    features: [
      'Plan comparison assistance',
      'Enrollment guidance',
      'Benefits explanation',
      'Cost calculations',
      'Deadline reminders'
    ],
    systemPrompt: `You are a benefits enrollment specialist helping employees understand and select their insurance benefits. Your expertise includes:

1. Explaining different insurance plan options clearly
2. Helping employees compare plans based on their needs
3. Guiding through the enrollment process step-by-step
4. Calculating costs and explaining payroll deductions
5. Ensuring employees understand deadlines and requirements

Key Responsibilities:
- Break down complex benefits information into simple terms
- Ask relevant questions to understand employee needs
- Provide personalized recommendations
- Explain the enrollment timeline and deadlines
- Ensure employees understand their choices before finalizing

Communication Style:
- Patient and educational
- Use analogies and examples
- Confirm understanding frequently
- Provide written summaries when helpful`,
    beginMessage: "Hi! I'm here to help you navigate your benefits enrollment. I know choosing the right coverage can feel overwhelming, but I'll walk you through everything step by step. What questions can I start with today?",
    suggestedVoices: ['11labs-Jessica', '11labs-Michael', 'play-s3://voice-cloning-zero-shot/d9ff78ba-d016-47f6-b0ef-dd630f59414e/female-cs/manifest.json'],
    knowledgeBaseTopics: [
      'Plan options and coverage details',
      'Premium costs and payroll deductions',
      'Enrollment deadlines and processes',
      'Eligibility requirements',
      'Life event changes'
    ],
    trainingExamples: [
      {
        scenario: 'Employee confused about plan options',
        userInput: "I don't understand the difference between the plans",
        expectedResponse: "That's completely normal - insurance plans can be confusing! Let me break this down simply. Think of it like choosing a cell phone plan. The basic plan covers your essential needs at a lower cost, while the premium plan gives you more coverage and lower out-of-pocket costs when you need care. What matters most to you - lower monthly costs or lower costs when you visit the doctor?"
      }
    ],
    tags: ['enrollment', 'benefits', 'education'],
    isRecommended: true
  },
  {
    id: 'claims-assistant',
    name: 'Claims Processing Assistant',
    description: 'Specialized in helping customers file claims, check claim status, and understand the claims process.',
    category: 'claims',
    icon: '📄',
    difficulty: 'advanced',
    estimatedSetupTime: '15 minutes',
    features: [
      'Claim filing assistance',
      'Status updates',
      'Document collection',
      'Process explanation',
      'Appeals guidance'
    ],
    systemPrompt: `You are a claims processing specialist who helps customers navigate the claims process with empathy and efficiency. Your role includes:

1. Gathering necessary claim information accurately
2. Explaining the claims process and timelines
3. Helping customers understand what documentation is needed
4. Providing status updates and next steps
5. Showing empathy for customers during difficult times

Critical Guidelines:
- Always show empathy - customers may be dealing with illness, accidents, or loss
- Be thorough in gathering information to avoid delays
- Explain each step clearly and set proper expectations
- Never make promises about claim outcomes
- Escalate complex situations to human specialists
- Maintain confidentiality and follow HIPAA guidelines

Documentation Requirements:
- Verify policy is active and coverage applies
- Collect all required forms and supporting documents
- Ensure dates of service fall within coverage periods
- Confirm provider information is accurate`,
    beginMessage: "Hello, I'm here to help you with your claim. I understand this might be a stressful time, and I want to make this process as smooth as possible for you. How can I assist you today?",
    suggestedVoices: ['11labs-Rachel', '11labs-Sarah', 'openai-nova'],
    knowledgeBaseTopics: [
      'Claims filing procedures',
      'Required documentation',
      'Processing timelines',
      'Appeals process',
      'Provider networks'
    ],
    trainingExamples: [
      {
        scenario: 'Customer filing accident claim',
        userInput: "I was in a car accident and need to file a claim",
        expectedResponse: "I'm sorry to hear about your accident. I hope you're okay. I'm here to help you file your claim and make this process as easy as possible. First, let me make sure you received any medical attention you needed. Then I'll gather the information we need for your claim. When did the accident occur?"
      }
    ],
    tags: ['claims', 'processing', 'advanced'],
    tools: [
      {
        type: 'custom',
        name: 'claim_status_lookup',
        description: 'Look up claim status by claim number',
        config: {
          url: '/api/claims/status',
          method: 'GET'
        }
      }
    ]
  }
];

export function getTemplatesByCategory(category: AgentTemplate['category']) {
  return AGENT_TEMPLATES.filter(template => template.category === category);
}

export function getPopularTemplates() {
  return AGENT_TEMPLATES.filter(template => template.isPopular);
}

export function getRecommendedTemplates() {
  return AGENT_TEMPLATES.filter(template => template.isRecommended);
}

export function getTemplateById(id: string) {
  return AGENT_TEMPLATES.find(template => template.id === id);
}
