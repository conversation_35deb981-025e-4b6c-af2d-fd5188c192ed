import { createClient } from '@supabase/supabase-js'
import type { RetellWebhookEvent } from '@/types/retell'

export async function handleCallWebhook(event: RetellWebhookEvent) {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  )

  // First, look up the agent
  const { data: agent } = await supabase
    .from('agents')
    .select('id')
    .eq('retell_agent_id', event.call.agent_id)
    .single()

  if (!agent) {
    throw new Error(`Unknown agent_id: ${event.call.agent_id}`)
  }

  // Insert the call record
  const { data, error } = await supabase
    .from('calls')
    .insert({
      call_id: event.call.call_id,
      agent_id: agent.id,
      retell_agent_id: event.call.agent_id,
      call_status: event.call.call_status,
      start_timestamp: new Date(event.call.start_timestamp).toISOString(),
      end_timestamp: new Date(event.call.end_timestamp).toISOString(),
      duration_ms: event.call.duration_ms,
      transcript: event.call.transcript,
      dynamic_variables: event.call.retell_llm_dynamic_variables,
      raw_data: event
    })
    .select()
    .single()

  if (error) throw error
  return data
} 