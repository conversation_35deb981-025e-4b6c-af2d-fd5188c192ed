import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

// Debug environment variables
console.log('Environment variables:', {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL,
  serviceKey: process.env.SUPABASE_SERVICE_KEY ? '[exists]' : '[missing]',
  nodeEnv: process.env.NODE_ENV
})

if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
}

if (!process.env.SUPABASE_SERVICE_KEY) {
  throw new Error('SUPABASE_SERVICE_KEY is required')
}

export const supabaseServiceRole = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY,
  {
    auth: {
      persistSession: false, // Don't persist the service role session
      autoRefreshToken: false,
    },
  }
) 