import { supabase } from './client'
import { CallPayload } from '@/types/database'
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js'

// Get all organizations
export async function getOrganizations() {
  const { data, error } = await supabase
    .from('organizations')
    .select(`
      *,
      organization_agents (
        id,
        name,
        type,
        status
      )
    `)
    .order('name')

  if (error) throw error
  return data
}

// Get organization details with agent count
export async function getOrganizationDetails(organizationId: string) {
  const { data, error } = await supabase
    .from('organizations')
    .select(`
      *,
      organization_agents (
        id,
        name,
        type,
        status
      ),
      calls (
        count
      )
    `)
    .eq('id', organizationId)
    .single()

  if (error) throw error
  return data
}

// Get recent calls for an organization
export async function getRecentCalls(organizationId: string, limit = 10) {
  const { data, error } = await supabase
    .from('calls')
    .select(`
      *,
      organization_agent:organization_agents (
        name,
        type
      )
    `)
    .eq('organization_id', organizationId)
    .order('start_timestamp', { ascending: false })
    .limit(limit)

  if (error) throw error
  return data
}

// Get analytics for an organization
export async function getOrganizationAnalytics(organizationId: string, days = 7) {
  const { data, error } = await supabase
    .from('call_analytics')
    .select('*')
    .eq('organization_id', organizationId)
    .gte('day', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
    .order('day')

  if (error) throw error
  return data
}

// Search call transcripts
export async function searchTranscripts(organizationId: string, query: string) {
  const { data, error } = await supabase
    .from('calls')
    .select(`
      call_id,
      start_timestamp,
      transcript,
      organization_agent:organization_agents (
        name
      )
    `)
    .eq('organization_id', organizationId)
    .textSearch('transcript', query)
    .order('start_timestamp', { ascending: false })

  if (error) throw error
  return data
}

// Real-time subscription to new calls
export function subscribeToNewCalls(organizationId: string, onCall: (call: CallPayload) => void) {
  return supabase
    .channel('new-calls')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'calls',
        filter: `organization_id=eq.${organizationId}`
      },
      (payload: RealtimePostgresChangesPayload<CallPayload>) => onCall(payload.new as CallPayload)
    )
    .subscribe()
} 