-- Add domain-based authentication system
-- This migration adds the ability to restrict user registration to pre-approved domains

-- Create organization_domains table
CREATE TABLE IF NOT EXISTS organization_domains (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  domain text NOT NULL UNIQUE,
  verified boolean DEFAULT false,
  verification_token text UNIQUE DEFAULT encode(gen_random_bytes(32), 'hex'),
  verification_method text DEFAULT 'dns' CHECK (verification_method IN ('dns', 'email', 'manual')),
  admin_approved boolean DEFAULT false,
  auto_assign_role text DEFAULT 'member' CHECK (auto_assign_role IN ('admin', 'member', 'viewer')),
  created_at timestamptz DEFAULT now(),
  verified_at timestamptz,
  approved_at timestamptz,
  approved_by uuid REFERENCES auth.users(id),
  created_by uuid REFERENCES auth.users(id),
  notes text
);

-- Create indexes for performance
CREATE INDEX idx_org_domains_domain ON organization_domains(domain);
CREATE INDEX idx_org_domains_org_id ON organization_domains(organization_id);
CREATE INDEX idx_org_domains_verified ON organization_domains(verified, admin_approved);

-- Enable RLS
ALTER TABLE organization_domains ENABLE ROW LEVEL SECURITY;

-- RLS policies for organization_domains
CREATE POLICY "Organization domains are viewable by organization members"
  ON organization_domains FOR SELECT
  USING (organization_id IN (
    SELECT organization_id FROM organization_members WHERE user_id = auth.uid()
  ));

CREATE POLICY "Organization admins can manage domains"
  ON organization_domains FOR ALL
  USING (organization_id IN (
    SELECT organization_id FROM organization_members 
    WHERE user_id = auth.uid() AND role = 'admin'
  ));

-- Function to extract domain from email
CREATE OR REPLACE FUNCTION extract_domain_from_email(email text)
RETURNS text AS $$
BEGIN
  RETURN LOWER(SPLIT_PART(email, '@', 2));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to check if domain is allowed for registration
CREATE OR REPLACE FUNCTION is_domain_allowed_for_registration(email text)
RETURNS boolean AS $$
DECLARE
  domain_name text;
  domain_record record;
BEGIN
  domain_name := extract_domain_from_email(email);
  
  -- Check if domain exists and is approved
  SELECT * INTO domain_record
  FROM organization_domains
  WHERE domain = domain_name
    AND verified = true
    AND admin_approved = true;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to get organization for domain
CREATE OR REPLACE FUNCTION get_organization_for_domain(email text)
RETURNS uuid AS $$
DECLARE
  domain_name text;
  org_id uuid;
BEGIN
  domain_name := extract_domain_from_email(email);
  
  SELECT organization_id INTO org_id
  FROM organization_domains
  WHERE domain = domain_name
    AND verified = true
    AND admin_approved = true;
  
  RETURN org_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get auto-assign role for domain
CREATE OR REPLACE FUNCTION get_auto_assign_role_for_domain(email text)
RETURNS text AS $$
DECLARE
  domain_name text;
  role_name text;
BEGIN
  domain_name := extract_domain_from_email(email);
  
  SELECT auto_assign_role INTO role_name
  FROM organization_domains
  WHERE domain = domain_name
    AND verified = true
    AND admin_approved = true;
  
  RETURN COALESCE(role_name, 'member');
END;
$$ LANGUAGE plpgsql;

-- Function to automatically assign user to organization on signup
CREATE OR REPLACE FUNCTION handle_new_user_organization_assignment()
RETURNS trigger AS $$
DECLARE
  user_email text;
  org_id uuid;
  user_role text;
BEGIN
  -- Get user email from the new user record
  user_email := NEW.email;
  
  -- Check if domain is allowed and get organization
  IF is_domain_allowed_for_registration(user_email) THEN
    org_id := get_organization_for_domain(user_email);
    user_role := get_auto_assign_role_for_domain(user_email);
    
    -- Insert organization membership
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (org_id, NEW.id, user_role)
    ON CONFLICT (organization_id, user_id) DO NOTHING;
    
    -- Log the assignment
    INSERT INTO organization_domains (organization_id, domain, verified, admin_approved, notes, created_by)
    VALUES (org_id, extract_domain_from_email(user_email), true, true, 
            'Auto-assigned user: ' || user_email, NEW.id)
    ON CONFLICT (domain) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic organization assignment
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user_organization_assignment();

-- Function to validate user can register with email domain
CREATE OR REPLACE FUNCTION validate_user_registration(email text)
RETURNS jsonb AS $$
DECLARE
  domain_name text;
  domain_record record;
  org_record record;
  result jsonb;
BEGIN
  domain_name := extract_domain_from_email(email);
  
  -- Check if domain exists and is approved
  SELECT * INTO domain_record
  FROM organization_domains
  WHERE domain = domain_name;
  
  IF NOT FOUND THEN
    result := jsonb_build_object(
      'allowed', false,
      'reason', 'domain_not_registered',
      'message', 'Your organization domain is not registered. Please contact your administrator.',
      'domain', domain_name
    );
  ELSIF NOT domain_record.verified THEN
    result := jsonb_build_object(
      'allowed', false,
      'reason', 'domain_not_verified',
      'message', 'Your organization domain is not yet verified. Please contact your administrator.',
      'domain', domain_name
    );
  ELSIF NOT domain_record.admin_approved THEN
    result := jsonb_build_object(
      'allowed', false,
      'reason', 'domain_not_approved',
      'message', 'Your organization domain is pending approval. Please contact your administrator.',
      'domain', domain_name
    );
  ELSE
    -- Get organization details
    SELECT * INTO org_record
    FROM organizations
    WHERE id = domain_record.organization_id;
    
    result := jsonb_build_object(
      'allowed', true,
      'domain', domain_name,
      'organization_id', domain_record.organization_id,
      'organization_name', org_record.name,
      'auto_assign_role', domain_record.auto_assign_role
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add some sample domains for testing
INSERT INTO organization_domains (organization_id, domain, verified, admin_approved, verification_method, auto_assign_role, notes, approved_at)
VALUES 
  (
    'd0816f44-4576-4b6f-8c45-a80464f0a6c8',
    'benefitsguide.com',
    true,
    true,
    'manual',
    'member',
    'Initial domain for Benefits Guide Agency',
    now()
  ),
  (
    'd0816f44-4576-4b6f-8c45-a80464f0a6c8',
    'example.com',
    true,
    true,
    'manual',
    'member',
    'Development domain for testing',
    now()
  )
ON CONFLICT (domain) DO NOTHING;

-- Update organization_members table to include invitation tracking
ALTER TABLE organization_members 
ADD COLUMN IF NOT EXISTS invited_by uuid REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS invited_at timestamptz,
ADD COLUMN IF NOT EXISTS joined_at timestamptz DEFAULT now();

-- Create audit log table for domain and organization changes
CREATE TABLE IF NOT EXISTS organization_audit_log (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id),
  action text NOT NULL,
  resource_type text NOT NULL,
  resource_id text,
  old_values jsonb,
  new_values jsonb,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

CREATE INDEX idx_audit_log_org_id ON organization_audit_log(organization_id);
CREATE INDEX idx_audit_log_user_id ON organization_audit_log(user_id);
CREATE INDEX idx_audit_log_created_at ON organization_audit_log(created_at);

-- Enable RLS on audit log
ALTER TABLE organization_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS policy for audit log
CREATE POLICY "Organization audit logs are viewable by organization admins"
  ON organization_audit_log FOR SELECT
  USING (organization_id IN (
    SELECT organization_id FROM organization_members 
    WHERE user_id = auth.uid() AND role = 'admin'
  ));

-- Function to log organization changes
CREATE OR REPLACE FUNCTION log_organization_change(
  p_organization_id uuid,
  p_action text,
  p_resource_type text,
  p_resource_id text DEFAULT NULL,
  p_old_values jsonb DEFAULT NULL,
  p_new_values jsonb DEFAULT NULL,
  p_metadata jsonb DEFAULT '{}'
)
RETURNS void AS $$
BEGIN
  INSERT INTO organization_audit_log (
    organization_id,
    user_id,
    action,
    resource_type,
    resource_id,
    old_values,
    new_values,
    metadata
  ) VALUES (
    p_organization_id,
    auth.uid(),
    p_action,
    p_resource_type,
    p_resource_id,
    p_old_values,
    p_new_values,
    p_metadata
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
