-- Add management fields to organizations
alter table organizations 
add column status text not null default 'active' check (status in ('active', 'inactive', 'suspended')),
add column settings jsonb default '{}'::jsonb;

-- Add organization_agents junction table for managing agent assignments
create table organization_agents (
  id uuid default gen_random_uuid() primary key,
  organization_id uuid references organizations(id) on delete cascade,
  agent_id text not null, -- This is the RetellAI agent_id
  name text not null,     -- Friendly name for the agent
  type text not null check (type in ('outbound_voice', 'inbound_voice', 'web_call')),
  status text not null default 'active' check (status in ('active', 'inactive')),
  settings jsonb default '{}'::jsonb,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique(organization_id, agent_id)
);

-- Add indexes for organization_agents
create index idx_org_agents_org on organization_agents(organization_id);
create index idx_org_agents_agent on organization_agents(agent_id);

-- Modify calls table to reference organization_agents
alter table calls
add column organization_id uuid references organizations(id),
add column organization_agent_id uuid references organization_agents(id);

-- Update call_analytics view
create or replace view call_analytics as
select 
  o.id as organization_id,
  o.name as organization_name,
  oa.id as agent_id,
  oa.name as agent_name,
  date_trunc('day', c.start_timestamp) as day,
  count(*) as total_calls,
  sum(duration_ms)::float / 1000 / 60 as total_minutes,
  count(*) filter (where c.call_status = 'completed') as completed_calls,
  count(*) filter (where c.call_status != 'completed') as incomplete_calls
from organizations o
join organization_agents oa on oa.organization_id = o.id
join calls c on c.organization_agent_id = oa.id
group by 1, 2, 3, 4, 5;

-- Add RLS policies for organization_agents
alter table organization_agents enable row level security;

create policy "Organization agents are viewable by organization members"
  on organization_agents for select
  using (organization_id in (
    select organization_id from organization_members where user_id = auth.uid()
  ));

-- Function to handle webhook and automatically associate with organization
create or replace function handle_call_webhook(payload jsonb)
returns uuid
language plpgsql
security definer
as $$
declare
  v_org_agent_id uuid;
  v_org_id uuid;
  v_call_id uuid;
begin
  -- Look up organization_agent and organization from RetellAI agent_id
  select oa.id, oa.organization_id into v_org_agent_id, v_org_id
  from organization_agents oa
  where oa.agent_id = payload->>'agent_id'
  and oa.status = 'active';

  if v_org_agent_id is null then
    raise exception 'Unknown or inactive agent_id: %', payload->>'agent_id';
  end if;

  -- Insert call record
  insert into calls (
    call_id,
    organization_id,
    organization_agent_id,
    retell_agent_id,
    call_status,
    start_timestamp,
    end_timestamp,
    duration_ms,
    transcript,
    dynamic_variables,
    raw_data
  ) values (
    payload->>'call_id',
    v_org_id,
    v_org_agent_id,
    payload->>'agent_id',
    payload->>'call_status',
    to_timestamp((payload->>'start_timestamp')::bigint / 1000),
    to_timestamp((payload->>'end_timestamp')::bigint / 1000),
    (payload->>'duration_ms')::integer,
    payload->>'transcript',
    payload->'retell_llm_dynamic_variables',
    payload
  )
  returning id into v_call_id;

  return v_call_id;
end;
$$; 