-- Add sync tracking to organizations table
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS settings jsonb DEFAULT '{}';

-- Create sync logs table
CREATE TABLE IF NOT EXISTS sync_logs (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  sync_type text NOT NULL CHECK (sync_type IN ('agents', 'calls', 'full')),
  trigger text NOT NULL CHECK (trigger IN ('manual', 'scheduled', 'webhook', 'api')),
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
  results jsonb NOT NULL DEFAULT '{}',
  success_count integer DEFAULT 0,
  failure_count integer DEFAULT 0,
  duration_ms integer,
  created_at timestamptz DEFAULT now(),
  created_by uuid REFERENCES auth.users(id)
);

-- <PERSON>reate indexes for sync logs
CREATE INDEX IF NOT EXISTS idx_sync_logs_type ON sync_logs(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_trigger ON sync_logs(trigger);
CREATE INDEX IF NOT EXISTS idx_sync_logs_organization ON sync_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at ON sync_logs(created_at);

-- Add sync metadata to organization_agents
ALTER TABLE organization_agents 
ADD COLUMN IF NOT EXISTS last_synced_at timestamptz,
ADD COLUMN IF NOT EXISTS sync_status text DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'error', 'manual'));

-- Create agent sync history table for detailed tracking
CREATE TABLE IF NOT EXISTS agent_sync_history (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_agent_id uuid REFERENCES organization_agents(id) ON DELETE CASCADE,
  agent_id text NOT NULL,
  action text NOT NULL CHECK (action IN ('created', 'updated', 'removed', 'reactivated')),
  changes jsonb DEFAULT '{}',
  retell_data jsonb,
  error_message text,
  created_at timestamptz DEFAULT now(),
  sync_log_id uuid REFERENCES sync_logs(id) ON DELETE SET NULL
);

-- Create indexes for agent sync history
CREATE INDEX IF NOT EXISTS idx_agent_sync_history_org_agent ON agent_sync_history(organization_agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_sync_history_agent_id ON agent_sync_history(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_sync_history_action ON agent_sync_history(action);
CREATE INDEX IF NOT EXISTS idx_agent_sync_history_created_at ON agent_sync_history(created_at);

-- Enable RLS on new tables
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_sync_history ENABLE ROW LEVEL SECURITY;

-- RLS policies for sync_logs
CREATE POLICY "Sync logs are viewable by organization admins" ON sync_logs
  FOR SELECT USING (
    organization_id IS NULL OR -- Global sync logs
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- RLS policies for agent_sync_history
CREATE POLICY "Agent sync history is viewable by organization members" ON agent_sync_history
  FOR SELECT USING (
    organization_agent_id IN (
      SELECT oa.id 
      FROM organization_agents oa
      JOIN organization_members om ON oa.organization_id = om.organization_id
      WHERE om.user_id = auth.uid()
    )
  );

-- Function to clean up old sync logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_sync_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM sync_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  DELETE FROM agent_sync_history 
  WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create a function to get sync status for an organization
CREATE OR REPLACE FUNCTION get_organization_sync_status(org_id uuid)
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  SELECT jsonb_build_object(
    'last_sync', (
      SELECT created_at 
      FROM sync_logs 
      WHERE organization_id = org_id OR organization_id IS NULL
      ORDER BY created_at DESC 
      LIMIT 1
    ),
    'total_agents', (
      SELECT COUNT(*) 
      FROM organization_agents 
      WHERE organization_id = org_id
    ),
    'active_agents', (
      SELECT COUNT(*) 
      FROM organization_agents 
      WHERE organization_id = org_id AND status = 'active'
    ),
    'pending_sync_agents', (
      SELECT COUNT(*) 
      FROM organization_agents 
      WHERE organization_id = org_id AND sync_status = 'pending'
    ),
    'last_sync_errors', (
      SELECT COUNT(*) 
      FROM sync_logs 
      WHERE organization_id = org_id AND failure_count > 0
      ORDER BY created_at DESC 
      LIMIT 1
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION cleanup_old_sync_logs() TO service_role;
GRANT EXECUTE ON FUNCTION get_organization_sync_status(uuid) TO authenticated;

-- Add helpful comments
COMMENT ON TABLE sync_logs IS 'Tracks all synchronization operations between the app and RetellAI';
COMMENT ON TABLE agent_sync_history IS 'Detailed history of agent synchronization changes';
COMMENT ON FUNCTION get_organization_sync_status(uuid) IS 'Returns comprehensive sync status for an organization';
