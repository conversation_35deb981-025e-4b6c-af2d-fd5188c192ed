-- Insert a test organization
insert into organizations (id, name, status, settings)
values (
  'd0816f44-4576-4b6f-8c45-a80464f0a6c8',
  'Benefits Guide Agency',
  'active',
  '{"timezone": "America/Chicago", "location": "Des Moines, Iowa"}'::jsonb
);

-- Insert a test organization_agent (VAL from the sample data)
insert into organization_agents (
  id,
  organization_id,
  agent_id,
  name,
  type,
  status,
  settings
) values (
  'e77cf417-af8c-4c79-8a89-fb447d431111',
  'd0816f44-4576-4b6f-8c45-a80464f0a6c8',
  'agent_dc88f52320b5362e45070c49e5',
  'VAL',
  'outbound_voice',
  'active',
  '{"description": "Virtual Assistant at Benefits Guide Agency"}'::jsonb
);

-- Insert test call using data from api-response.json
insert into calls (
  call_id,
  organization_id,
  organization_agent_id,
  retell_agent_id,
  call_status,
  start_timestamp,
  end_timestamp,
  duration_ms,
  transcript,
  dynamic_variables,
  raw_data
) values (
  'call_bb915c78daa96954cd1a025b16c',
  'd0816f44-4576-4b6f-8c45-a80464f0a6c8',
  'e77cf417-af8c-4c79-8a89-fb447d431111',
  'agent_dc88f52320b5362e45070c49e5',
  'ended',
  to_timestamp(1736795076115::bigint / 1000),
  to_timestamp(1736795201931::bigint / 1000),
  125816,
  'Agent: "Hello Tom Foolery,  I understand you''re interested in learning more about optional employee benefits..."',
  '{"customer_name": "Tom Foolery"}'::jsonb,
  '{
    "call_id": "call_bb915c78daa96954cd1a025b16c",
    "agent_id": "agent_dc88f52320b5362e45070c49e5",
    "call_status": "ended",
    "start_timestamp": 1736795076115,
    "end_timestamp": 1736795201931,
    "duration_ms": 125816,
    "retell_llm_dynamic_variables": {"customer_name": "Tom Foolery"}
  }'::jsonb
); 