-- Add domain normalization trigger to ensure all domains are consistently normalized
-- This migration adds a trigger to automatically normalize domains on insert/update

-- Function to normalize domain on insert/update
CREATE OR REPLACE FUNCTION normalize_domain_on_change()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Normalize the domain before inserting/updating
  NEW.domain := normalize_domain(NEW.domain);
  
  -- Validate domain format
  IF NOT is_valid_domain_format(NEW.domain) THEN
    RAISE EXCEPTION 'Invalid domain format: %', NEW.domain;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for domain normalization
DROP TRIGGER IF EXISTS normalize_domain_trigger ON organization_domains;
CREATE TRIGGER normalize_domain_trigger
  BEFORE INSERT OR UPDATE OF domain ON organization_domains
  FOR EACH ROW EXECUTE FUNCTION normalize_domain_on_change();

-- Update existing domains to be normalized (one-time cleanup)
UPDATE organization_domains 
SET domain = normalize_domain(domain)
WHERE domain != normalize_domain(domain);

-- Add constraint to prevent empty domains
ALTER TABLE organization_domains 
ADD CONSTRAINT check_domain_not_empty 
CHECK (LENGTH(TRIM(domain)) > 0);

-- Add constraint to ensure domain format is valid
ALTER TABLE organization_domains 
ADD CONSTRAINT check_domain_format_valid 
CHECK (is_valid_domain_format(domain));

-- Create a function to safely check domain availability (for API use)
CREATE OR REPLACE FUNCTION check_domain_availability(domain_input text)
RETURNS jsonb AS $$
DECLARE
  normalized_domain text;
  existing_count integer;
  existing_org_name text;
  result jsonb;
BEGIN
  -- Normalize the input domain
  normalized_domain := normalize_domain(domain_input);
  
  -- Validate format
  IF NOT is_valid_domain_format(normalized_domain) THEN
    result := jsonb_build_object(
      'available', false,
      'error', 'invalid_format',
      'message', 'Invalid domain format',
      'domain', normalized_domain
    );
    RETURN result;
  END IF;
  
  -- Check if domain exists
  SELECT COUNT(*), MAX(o.name) INTO existing_count, existing_org_name
  FROM organization_domains od
  LEFT JOIN organizations o ON od.organization_id = o.id
  WHERE od.domain = normalized_domain;
  
  IF existing_count > 0 THEN
    result := jsonb_build_object(
      'available', false,
      'error', 'domain_taken',
      'message', 'Domain is already registered to: ' || COALESCE(existing_org_name, 'Unknown Organization'),
      'domain', normalized_domain
    );
  ELSE
    result := jsonb_build_object(
      'available', true,
      'message', 'Domain is available',
      'domain', normalized_domain
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create index for faster domain lookups (case-insensitive)
CREATE INDEX IF NOT EXISTS idx_org_domains_domain_normalized 
ON organization_domains(LOWER(domain));

-- Add audit logging for domain changes
CREATE OR REPLACE FUNCTION log_domain_changes()
RETURNS trigger AS $$
BEGIN
  -- Log domain creation
  IF TG_OP = 'INSERT' THEN
    PERFORM log_organization_change(
      NEW.organization_id,
      'create',
      'domain',
      NEW.id::text,
      NULL,
      to_jsonb(NEW),
      jsonb_build_object('trigger', 'domain_insert')
    );
    RETURN NEW;
  END IF;
  
  -- Log domain updates
  IF TG_OP = 'UPDATE' THEN
    PERFORM log_organization_change(
      NEW.organization_id,
      'update',
      'domain',
      NEW.id::text,
      to_jsonb(OLD),
      to_jsonb(NEW),
      jsonb_build_object('trigger', 'domain_update')
    );
    RETURN NEW;
  END IF;
  
  -- Log domain deletion
  IF TG_OP = 'DELETE' THEN
    PERFORM log_organization_change(
      OLD.organization_id,
      'delete',
      'domain',
      OLD.id::text,
      to_jsonb(OLD),
      NULL,
      jsonb_build_object('trigger', 'domain_delete')
    );
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for domain audit logging
DROP TRIGGER IF EXISTS log_domain_changes_trigger ON organization_domains;
CREATE TRIGGER log_domain_changes_trigger
  AFTER INSERT OR UPDATE OR DELETE ON organization_domains
  FOR EACH ROW EXECUTE FUNCTION log_domain_changes();
