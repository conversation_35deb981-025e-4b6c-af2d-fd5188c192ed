-- Enhanced Multi-Tenancy Migration
-- This migration enhances the existing schema for proper multi-tenant SaaS

-- Add missing columns to organizations table
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS slug text UNIQUE;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS settings jsonb DEFAULT '{}';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS plan text DEFAULT 'free' CHECK (plan IN ('free', 'starter', 'professional', 'enterprise'));
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS status text DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'cancelled'));
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS billing_email text;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS max_agents integer DEFAULT 3;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS max_calls_per_month integer DEFAULT 1000;

-- Create organization_agents table (if not exists)
CREATE TABLE IF NOT EXISTS organization_agents (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  agent_id text NOT NULL, -- RetellAI agent ID
  name text NOT NULL,
  type text NOT NULL CHECK (type IN ('inbound_voice', 'outbound_voice', 'web_call')),
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
  settings jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(organization_id, agent_id)
);

-- Create organization_invitations table
CREATE TABLE IF NOT EXISTS organization_invitations (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  email text NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'member', 'viewer')),
  token text UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
  expires_at timestamptz NOT NULL DEFAULT (now() + interval '7 days'),
  accepted_at timestamptz,
  invited_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  UNIQUE(organization_id, email)
);

-- Create organization_usage table for tracking usage
CREATE TABLE IF NOT EXISTS organization_usage (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  month date NOT NULL, -- First day of the month
  total_calls integer DEFAULT 0,
  total_minutes numeric DEFAULT 0,
  total_agents integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(organization_id, month)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_org_agents_org_id ON organization_agents(organization_id);
CREATE INDEX IF NOT EXISTS idx_org_agents_agent_id ON organization_agents(agent_id);
CREATE INDEX IF NOT EXISTS idx_org_invitations_token ON organization_invitations(token);
CREATE INDEX IF NOT EXISTS idx_org_invitations_email ON organization_invitations(email);
CREATE INDEX IF NOT EXISTS idx_org_usage_org_month ON organization_usage(organization_id, month);

-- Enable RLS on new tables
ALTER TABLE organization_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies for organization_agents
DROP POLICY IF EXISTS "Organization agents are viewable by organization members" ON organization_agents;
CREATE POLICY "Organization agents are viewable by organization members"
  ON organization_agents FOR SELECT
  USING (organization_id IN (
    SELECT organization_id FROM organization_members WHERE user_id = auth.uid()
  ));

DROP POLICY IF EXISTS "Organization agents are manageable by organization admins" ON organization_agents;
CREATE POLICY "Organization agents are manageable by organization admins"
  ON organization_agents FOR ALL
  USING (organization_id IN (
    SELECT organization_id FROM organization_members
    WHERE user_id = auth.uid() AND role = 'admin'
  ));

-- RLS Policies for organization_invitations
DROP POLICY IF EXISTS "Organization invitations are viewable by organization admins" ON organization_invitations;
CREATE POLICY "Organization invitations are viewable by organization admins"
  ON organization_invitations FOR SELECT
  USING (organization_id IN (
    SELECT organization_id FROM organization_members
    WHERE user_id = auth.uid() AND role = 'admin'
  ));

DROP POLICY IF EXISTS "Organization invitations are manageable by organization admins" ON organization_invitations;
CREATE POLICY "Organization invitations are manageable by organization admins"
  ON organization_invitations FOR ALL
  USING (organization_id IN (
    SELECT organization_id FROM organization_members
    WHERE user_id = auth.uid() AND role = 'admin'
  ));

-- RLS Policies for organization_usage
DROP POLICY IF EXISTS "Organization usage is viewable by organization members" ON organization_usage;
CREATE POLICY "Organization usage is viewable by organization members"
  ON organization_usage FOR SELECT
  USING (organization_id IN (
    SELECT organization_id FROM organization_members WHERE user_id = auth.uid()
  ));

-- Update existing organizations with slugs (if they don't have them)
UPDATE organizations
SET slug = lower(regexp_replace(name, '[^a-zA-Z0-9]+', '-', 'g'))
WHERE slug IS NULL;

-- Create function to automatically update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organization_agents_updated_at BEFORE UPDATE ON organization_agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organization_members_updated_at BEFORE UPDATE ON organization_members
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organization_usage_updated_at BEFORE UPDATE ON organization_usage
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create view for organization stats
CREATE OR REPLACE VIEW organization_stats AS
SELECT
  o.id,
  o.name,
  o.slug,
  o.plan,
  o.status,
  COUNT(DISTINCT oa.id) as total_agents,
  COUNT(DISTINCT om.id) as total_members,
  COALESCE(ou.total_calls, 0) as current_month_calls,
  COALESCE(ou.total_minutes, 0) as current_month_minutes,
  o.max_agents,
  o.max_calls_per_month,
  o.created_at
FROM organizations o
LEFT JOIN organization_agents oa ON oa.organization_id = o.id AND oa.status = 'active'
LEFT JOIN organization_members om ON om.organization_id = o.id
LEFT JOIN organization_usage ou ON ou.organization_id = o.id AND ou.month = date_trunc('month', now())
GROUP BY o.id, o.name, o.slug, o.plan, o.status, o.max_agents, o.max_calls_per_month, o.created_at, ou.total_calls, ou.total_minutes;

-- Grant necessary permissions
GRANT SELECT ON organization_stats TO authenticated;

-- Create function to get user's organization context
CREATE OR REPLACE FUNCTION get_user_organization()
RETURNS uuid AS $$
BEGIN
  RETURN (
    SELECT organization_id
    FROM organization_members
    WHERE user_id = auth.uid()
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is organization admin
CREATE OR REPLACE FUNCTION is_organization_admin(org_id uuid DEFAULT NULL)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM organization_members
    WHERE user_id = auth.uid()
    AND organization_id = COALESCE(org_id, get_user_organization())
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
