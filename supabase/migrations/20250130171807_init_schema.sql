-- Create organizations table
create table organizations (
  id uuid default gen_random_uuid() primary key,
  name text not null,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Create agents table
create table agents (
  id uuid default gen_random_uuid() primary key,
  retell_agent_id text not null unique,
  organization_id uuid references organizations(id) on delete cascade,
  name text not null,
  type text not null check (type in ('outbound_voice', 'inbound_voice', 'web_call')),
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Create calls table
create table calls (
  id uuid default gen_random_uuid() primary key,
  call_id text unique not null,
  agent_id uuid references agents(id),
  retell_agent_id text not null,
  call_status text not null,
  start_timestamp timestamptz not null,
  end_timestamp timestamptz not null,
  duration_ms integer not null,
  transcript text,
  dynamic_variables jsonb,
  raw_data jsonb not null,
  created_at timestamptz default now()
);

-- Create indexes
create index idx_calls_agent_id on calls(agent_id);
create index idx_calls_start_timestamp on calls(start_timestamp);
create index idx_calls_call_status on calls(call_status);
create index idx_agents_organization on agents(organization_id);
create index idx_calls_transcript_search on calls using gin(to_tsvector('english', transcript));

-- Create analytics view
create or replace view call_analytics as
select 
  o.id as organization_id,
  o.name as organization_name,
  a.id as agent_id,
  a.name as agent_name,
  date_trunc('day', c.start_timestamp) as day,
  count(*) as total_calls,
  sum(duration_ms)::float / 1000 / 60 as total_minutes,
  count(*) filter (where c.call_status = 'completed') as completed_calls,
  count(*) filter (where c.call_status != 'completed') as incomplete_calls
from organizations o
join agents a on a.organization_id = o.id
join calls c on c.agent_id = a.id
group by 1, 2, 3, 4, 5;

-- Add this before the RLS policies
-- Create organization members table
create table organization_members (
  id uuid default gen_random_uuid() primary key,
  organization_id uuid references organizations(id) on delete cascade,
  user_id uuid references auth.users(id) on delete cascade,
  role text not null check (role in ('admin', 'member')),
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique(organization_id, user_id)
);

create index idx_org_members_user on organization_members(user_id);
create index idx_org_members_org on organization_members(organization_id);

-- Enable RLS on organization members
alter table organization_members enable row level security;

-- Add organization members policies
create policy "Organization members are viewable by organization members"
  on organization_members for select
  using (organization_id in (
    select organization_id from organization_members where user_id = auth.uid()
  ));

-- Enable RLS
alter table organizations enable row level security;
alter table agents enable row level security;
alter table calls enable row level security;

-- Create RLS policies
create policy "Organizations are viewable by their members"
  on organizations for select
  using (auth.uid() in (
    select user_id from organization_members where organization_id = id
  ));

create policy "Agents are viewable by organization members"
  on agents for select
  using (organization_id in (
    select organization_id from organization_members where user_id = auth.uid()
  ));

create policy "Calls are viewable by organization members"
  on calls for select
  using (agent_id in (
    select a.id from agents a
    join organization_members om on om.organization_id = a.organization_id
    where om.user_id = auth.uid()
  ));
