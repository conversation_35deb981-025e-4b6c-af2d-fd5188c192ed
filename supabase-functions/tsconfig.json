{"compilerOptions": {"allowJs": true, "strict": true, "noImplicitAny": true, "noImplicitThis": true, "alwaysStrict": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "module": "esnext", "target": "esnext", "useDefineForClassFields": true, "lib": ["esnext"], "types": ["@supabase/supabase-js"], "moduleResolution": "node"}, "include": ["**/*.ts"], "exclude": ["node_modules"]}