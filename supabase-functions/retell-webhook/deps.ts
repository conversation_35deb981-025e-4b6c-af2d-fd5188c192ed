// Add JSDoc comment to specify Deno types
/** @jsxImportSource https://deno.land/x/fresh@1.6.5/runtime.ts */

import { serve } from "https://deno.land/std@0.208.0/http/server.ts"
import { createClient } from "https://deno.land/x/supabase@1.127.4/mod.ts"
import type { SupabaseClient } from "https://deno.land/x/supabase@1.127.4/mod.ts"

export { serve, createClient, type SupabaseClient }

// Add Deno types
declare global {
  const Deno: {
    env: {
      get(key: string): string | undefined
    }
  }
} 