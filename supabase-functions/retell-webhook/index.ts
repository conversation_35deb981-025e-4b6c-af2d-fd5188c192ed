import { serve, createClient, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./deps"

interface RetellWebhookEvent {
  event: "call_started" | "call_ended" | "call_analyzed"
  call: {
    call_id: string
    agent_id: string
    call_status: string
    start_timestamp: number
    end_timestamp: number
    duration_ms: number
    retell_llm_dynamic_variables: Record<string, string>
    transcript?: string
    recording_url?: string
    call_analysis?: {
      call_summary: string
      user_sentiment: string
      call_successful: boolean
      agent_task_completion_rating: string
      call_completion_rating: string
    }
    call_cost?: {
      combined_cost: number
      total_duration_seconds: number
    }
  }
}

async function handleCallAnalyzed(supabaseClient: SupabaseClient, call: RetellWebhookEvent['call']) {
  console.log('Handling call_analyzed event:', call.call_id)
  
  try {
    // First, look up the organization_agent
    const { data: agentData, error: agentError } = await supabaseClient
      .from('organization_agents')
      .select('organization_id, id')
      .eq('agent_id', call.agent_id)
      .single()

    if (agentError) {
      console.error('Agent lookup error:', agentError)
      // For testing, use default IDs
      const defaultData = {
        organization_id: '11111111-1111-1111-1111-111111111111',
        id: '22222222-2222-2222-2222-222222222222'
      }
      console.log('Using default organization and agent IDs:', defaultData)
      return defaultData
    }

    const { data, error } = await supabaseClient
      .from('calls')
      .upsert({
        call_id: call.call_id,
        organization_id: agentData.organization_id,
        organization_agent_id: agentData.id,
        retell_agent_id: call.agent_id,
        call_status: call.call_status,
        start_timestamp: new Date(call.start_timestamp).toISOString(),
        end_timestamp: new Date(call.end_timestamp).toISOString(),
        duration_ms: call.duration_ms,
        duration_seconds: Math.floor(call.duration_ms / 1000),
        transcript: call.transcript,
        dynamic_variables: call.retell_llm_dynamic_variables,
        raw_data: call,
        recording_url: call.recording_url,
        call_summary: call.call_analysis?.call_summary,
        user_sentiment: call.call_analysis?.user_sentiment,
        call_successful: call.call_analysis?.call_successful,
        task_completion: call.call_analysis?.agent_task_completion_rating,
        completion_rating: call.call_analysis?.call_completion_rating,
        cost: call.call_cost?.combined_cost || 0
      })
      .select()
      .single()

    if (error) {
      console.error('Error inserting call:', error)
      throw error
    }

    console.log('Successfully inserted call:', data)
    return { data }
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error in handleCallAnalyzed:', error.message)
      throw error
    }
    throw new Error('Unknown error in handleCallAnalyzed')
  }
}

serve(async (req: Request) => {
  try {
    const supabaseClient = createClient(
      Deno.env.get('DB_URL') ?? '',
      Deno.env.get('SERVICE_ROLE_KEY') ?? ''
    )

    const body: RetellWebhookEvent = await req.json()
    console.log('Received webhook:', body.event)

    let result
    switch (body.event) {
      case 'call_analyzed':
        result = await handleCallAnalyzed(supabaseClient, body.call)
        break
      default:
        return new Response(
          JSON.stringify({ error: 'Unsupported event type' }), 
          { status: 400 }
        )
    }

    return new Response(
      JSON.stringify({ success: true, data: result }), 
      { status: 200 }
    )

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Webhook error:', error.message)
      return new Response(
        JSON.stringify({ error: error.message }), 
        { status: 500 }
      )
    }
    return new Response(
      JSON.stringify({ error: 'Unknown error' }), 
      { status: 500 }
    )
  }
})