-- Create a test user and organization membership
-- This is for development/testing purposes only

-- First, let's create a test user in auth.users (you'll need to do this manually or through Supabase Auth)
-- For now, we'll create a placeholder that you can update with a real user ID

-- Example of creating organization membership for a test user
-- Replace 'YOUR_USER_ID_HERE' with an actual user ID from auth.users

/*
-- Create test organization membership
INSERT INTO organization_members (organization_id, user_id, role) VALUES
('d0816f44-4576-4b6f-8c45-a80464f0a6c8', 'YOUR_USER_ID_HERE', 'admin');

-- Verify the membership
SELECT 
  om.id,
  om.role,
  o.name as organization_name,
  u.email as user_email
FROM organization_members om
JOIN organizations o ON o.id = om.organization_id
JOIN auth.users u ON u.id = om.user_id;
*/

-- For now, let's just verify our organizations are set up correctly
SELECT 
  id,
  name,
  slug,
  plan,
  status,
  max_agents,
  max_calls_per_month
FROM organizations 
ORDER BY name;

-- Check organization agents
SELECT 
  o.name as organization,
  oa.name as agent_name,
  oa.agent_id,
  oa.type,
  oa.status
FROM organization_agents oa
JOIN organizations o ON o.id = oa.organization_id
ORDER BY o.name, oa.name;

-- Check usage data
SELECT 
  o.name as organization,
  ou.total_calls,
  ou.total_minutes,
  ou.total_agents,
  ROUND((ou.total_calls::numeric / o.max_calls_per_month * 100), 1) as usage_percentage
FROM organizations o
LEFT JOIN organization_usage ou ON ou.organization_id = o.id AND ou.month = date_trunc('month', now())
ORDER BY o.name;
