// <PERSON>ript to add missing RetellAI agents to the organization
// Run this with: npx tsx scripts/add-missing-agents.ts

const ORGANIZATION_ID = 'd0816f44-4576-4b6f-8c45-a80464f0a6c8'
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'

async function addMissingAgents() {
  try {
    console.log('🔍 Fetching missing agents...')
    
    // Get missing agents
    const missingResponse = await fetch(`${BASE_URL}/api/organization/agents/missing`)
    const missingData = await missingResponse.json()
    
    if (!missingResponse.ok) {
      throw new Error(`Failed to fetch missing agents: ${missingData.error}`)
    }
    
    console.log(`📊 Found ${missingData.missing_agents} missing agents out of ${missingData.total_retell_agents} total`)
    
    if (missingData.missing_agents === 0) {
      console.log('✅ All agents are already added to the organization!')
      return
    }
    
    console.log('\n🤖 Missing agents:')
    missingData.agents.forEach((agent: any, index: number) => {
      console.log(`${index + 1}. ${agent.agent_name} (${agent.agent_id})`)
    })
    
    console.log('\n➕ Adding missing agents to organization...')
    
    // Add each missing agent
    for (const agent of missingData.agents) {
      try {
        const addResponse = await fetch(`${BASE_URL}/api/organization/agents`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            agent_id: agent.agent_id,
            name: agent.agent_name,
            type: 'inbound_voice', // Default type
            organization_id: ORGANIZATION_ID
          })
        })
        
        const addData = await addResponse.json()
        
        if (addResponse.ok) {
          console.log(`✅ Added: ${agent.agent_name}`)
        } else {
          console.log(`❌ Failed to add ${agent.agent_name}: ${addData.error}`)
        }
      } catch (error) {
        console.log(`❌ Error adding ${agent.agent_name}:`, error)
      }
    }
    
    console.log('\n🎉 Finished adding agents!')
    
    // Verify the results
    console.log('\n🔍 Verifying results...')
    const verifyResponse = await fetch(`${BASE_URL}/api/organization/agents/missing`)
    const verifyData = await verifyResponse.json()
    
    if (verifyResponse.ok) {
      console.log(`📊 Now ${verifyData.missing_agents} missing agents out of ${verifyData.total_retell_agents} total`)
      if (verifyData.missing_agents === 0) {
        console.log('🎉 All agents successfully added!')
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

// Run the script
addMissingAgents().catch(console.error)
