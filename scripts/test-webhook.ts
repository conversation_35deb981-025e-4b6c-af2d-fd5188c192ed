const testPayload = {
  call_id: "call_bb915c78daa96954cd1a025b16c",
  agent_id: "agent_dc88f52320b5362e45070c49e5",
  call_status: "ended",
  start_timestamp: 1736795076115,
  end_timestamp: 1736795201931,
  duration_ms: 125816,
  retell_llm_dynamic_variables: { customer_name: "Tom Foolery" }
}

async function testWebhook() {
  const response = await fetch('http://localhost:3000/api/webhook/retell', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(testPayload)
  })

  const data = await response.json()
  console.log('Response:', data)
}

testWebhook().catch(console.error) 