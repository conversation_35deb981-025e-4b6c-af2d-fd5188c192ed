"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

type AgentValue = "all" | string // "all" for all agents, or specific agent name

interface AgentContextType {
  selectedAgent: AgentValue
  setSelectedAgent: (agent: AgentValue) => void
  availableAgents: string[]
  setAvailableAgents: (agents: string[]) => void
  getAgentLabel: (agent: AgentValue) => string
  isLoading: boolean
}

const AgentContext = createContext<AgentContextType | undefined>(undefined)

interface AgentProviderProps {
  children: ReactNode
}

export function AgentProvider({ children }: AgentProviderProps) {
  const [selectedAgent, setSelectedAgentState] = useState<AgentValue>("all")
  const [availableAgents, setAvailableAgents] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Load selected agent from localStorage on mount
  useEffect(() => {
    const savedAgent = localStorage.getItem('dashboard-selected-agent')
    if (savedAgent && (savedAgent === "all" || availableAgents.includes(savedAgent))) {
      setSelectedAgentState(savedAgent as AgentValue)
    }
  }, [availableAgents])

  // Fetch available agents on mount
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/agents')
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const agents = await response.json()
        const agentNames = agents.map((agent: any) => agent.name).filter(Boolean).sort()
        console.log('🏢 Loaded available agents for global context:', agentNames)
        setAvailableAgents(agentNames)
      } catch (err) {
        console.error('Error fetching agents for global context:', err)
        setAvailableAgents([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchAgents()
  }, [])

  // Save selected agent to localStorage when it changes
  const setSelectedAgent = (newAgent: AgentValue) => {
    setSelectedAgentState(newAgent)
    localStorage.setItem('dashboard-selected-agent', newAgent)
  }

  // Helper function to get display label
  const getAgentLabel = (agent: AgentValue): string => {
    if (agent === "all") {
      return "All Agents"
    }
    return agent
  }

  const value = {
    selectedAgent,
    setSelectedAgent,
    availableAgents,
    setAvailableAgents,
    getAgentLabel,
    isLoading
  }

  return (
    <AgentContext.Provider value={value}>
      {children}
    </AgentContext.Provider>
  )
}

export function useAgent() {
  const context = useContext(AgentContext)
  if (context === undefined) {
    throw new Error('useAgent must be used within an AgentProvider')
  }
  return context
}

// Export types for use in other components
export type { AgentValue }
