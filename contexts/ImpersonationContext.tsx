"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

interface ImpersonatedUser {
  id: string;
  email: string;
  role: string;
  organization_id: string;
}

interface OriginalAdmin {
  id: string;
  email: string;
}

interface ImpersonationContextType {
  isImpersonating: boolean;
  impersonatedUser: ImpersonatedUser | null;
  originalAdmin: OriginalAdmin | null;
  startImpersonation: (userId: string) => Promise<void>;
  stopImpersonation: () => Promise<void>;
  loading: boolean;
  error: string | null;
}

const ImpersonationContext = createContext<ImpersonationContextType | undefined>(undefined);

export function useImpersonation() {
  const context = useContext(ImpersonationContext);
  if (context === undefined) {
    throw new Error('useImpersonation must be used within an ImpersonationProvider');
  }
  return context;
}

interface ImpersonationProviderProps {
  children: React.ReactNode;
}

export function ImpersonationProvider({ children }: ImpersonationProviderProps) {
  const [isImpersonating, setIsImpersonating] = useState(false);
  const [impersonatedUser, setImpersonatedUser] = useState<ImpersonatedUser | null>(null);
  const [originalAdmin, setOriginalAdmin] = useState<OriginalAdmin | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check for existing impersonation session on mount
  useEffect(() => {
    checkImpersonationStatus();
  }, []);

  const checkImpersonationStatus = async () => {
    try {
      // Check if impersonation cookies exist (client-side check)
      const isActive = document.cookie.includes('impersonation_active=true');
      setIsImpersonating(isActive);
      
      if (isActive) {
        // Get impersonation details from server
        const response = await fetch('/api/admin/impersonate/status');
        if (response.ok) {
          const data = await response.json();
          setImpersonatedUser(data.impersonatedUser);
          setOriginalAdmin(data.originalAdmin);
        }
      }
    } catch (error) {
      console.error('Error checking impersonation status:', error);
    }
  };

  const startImpersonation = async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/impersonate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ targetUserId: userId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start impersonation');
      }

      setIsImpersonating(true);
      setImpersonatedUser(data.impersonatedUser);
      setOriginalAdmin(data.originalAdmin);

      // Reload the page to apply the impersonation context
      window.location.reload();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start impersonation';
      setError(errorMessage);
      console.error('Impersonation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const stopImpersonation = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/impersonate', {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to stop impersonation');
      }

      setIsImpersonating(false);
      setImpersonatedUser(null);
      setOriginalAdmin(null);

      // Reload the page to clear the impersonation context
      window.location.reload();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop impersonation';
      setError(errorMessage);
      console.error('Stop impersonation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    isImpersonating,
    impersonatedUser,
    originalAdmin,
    startImpersonation,
    stopImpersonation,
    loading,
    error,
  };

  return (
    <ImpersonationContext.Provider value={value}>
      {children}
    </ImpersonationContext.Provider>
  );
}
