"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

type TimeframeValue = "today" | "7" | "14" | "30" | "90" | "all"

interface TimeframeContextType {
  timeframe: TimeframeValue
  setTimeframe: (timeframe: TimeframeValue) => void
  getTimeframeLabel: (timeframe: TimeframeValue) => string
}

const TimeframeContext = createContext<TimeframeContextType | undefined>(undefined)

interface TimeframeProviderProps {
  children: ReactNode
}

export function TimeframeProvider({ children }: TimeframeProviderProps) {
  const [timeframe, setTimeframeState] = useState<TimeframeValue>("7")

  // Load timeframe from localStorage on mount
  useEffect(() => {
    const savedTimeframe = localStorage.getItem('dashboard-timeframe')
    if (savedTimeframe && ["today", "7", "14", "30", "90", "all"].includes(savedTimeframe)) {
      setTimeframeState(savedTimeframe as TimeframeValue)
    }
  }, [])

  // Save timeframe to localStorage when it changes
  const setTimeframe = (newTimeframe: TimeframeValue) => {
    setTimeframeState(newTimeframe)
    localStorage.setItem('dashboard-timeframe', newTimeframe)
  }

  // Helper function to get display label
  const getTimeframeLabel = (timeframe: TimeframeValue): string => {
    switch (timeframe) {
      case "today":
        return "Today"
      case "7":
        return "Last 7 days"
      case "14":
        return "Last 14 days"
      case "30":
        return "Last 30 days"
      case "90":
        return "Last 90 days"
      case "all":
        return "All Time"
      default:
        return "Last 7 days"
    }
  }

  const value = {
    timeframe,
    setTimeframe,
    getTimeframeLabel
  }

  return (
    <TimeframeContext.Provider value={value}>
      {children}
    </TimeframeContext.Provider>
  )
}

export function useTimeframe() {
  const context = useContext(TimeframeContext)
  if (context === undefined) {
    throw new Error('useTimeframe must be used within a TimeframeProvider')
  }
  return context
}

// Export types for use in other components
export type { TimeframeValue }
