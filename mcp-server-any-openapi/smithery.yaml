# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - openapiJsonDocsUrl
      - mcpApiPrefix
    properties:
      openapiJsonDocsUrl:
        type: string
        description: URL to the OpenAPI specification JSON.
      mcpApiPrefix:
        type: string
        description: Customizable tool namespace prefix.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({command: 'python', args: ['-m', 'mcp_server_any_openapi.server'], env: {OPENAPI_JSON_DOCS_URL: config.openapiJsonDocsUrl, MCP_API_PREFIX: config.mcpApiPrefix}})
