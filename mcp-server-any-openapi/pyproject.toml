[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mcp-server-any-openapi"
version = "0.1.0"
description = "MCP server for Any OpenAPI resources"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.10"
dependencies = [
    "mcp",
    "pydantic",
    "requests",
    "pytz",
    "pyyaml",
    "faiss-cpu",
    "numpy",
    "sentence-transformers",
    "fastapi",
    "uvicorn",
    "huggingface-hub"
]

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_server_any_openapi"]

[project.scripts]
mcp-server-any-openapi = "mcp_server_any_openapi:main"