# 🚀 Vercel Environment Variables Setup

## 📋 **Required Environment Variables**

Your Vercel deployment needs these environment variables to work properly:

### **1. Supabase Configuration**
```bash
NEXT_PUBLIC_SUPABASE_URL=https://zylxkmorpjycvjpvqeur.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5bHhrbW9ycGp5Y3ZqcHZxZXVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzgyNTU4MTgsImV4cCI6MjA1MzgzMTgxOH0.EIt6w8DjQNkuzFhD9Ac764yselLBHJTmzP1C7XvXOcs
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5bHhrbW9ycGp5Y3ZqcHZxZXVyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczODI1NTgxOCwiZXhwIjoyMDUzODMxODE4fQ.6G6rTVZqNskZEz4ehSZnEYVzoQ3rgmrPxhv21nw7taQ
```

### **2. RetellAI Configuration**
```bash
RETELL_API_KEY=key_4b6101fbf199cd72d280ab08cb91
NEXT_PUBLIC_RETELL_API_KEY=key_4b6101fbf199cd72d280ab08cb91
RETELL_LLM_ID=gpt-4o-mini
```

### **3. Multi-Tenant Configuration**
```bash
DEFAULT_ORGANIZATION_ID=d0816f44-4576-4b6f-8c45-a80464f0a6c8
```

### **4. Optional Configuration**
```bash
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://your-app-name.vercel.app
```

## 🔧 **How to Set Environment Variables in Vercel**

### **Method 1: Vercel Dashboard (Recommended)**

1. **Go to Vercel Dashboard**
   - Visit: https://vercel.com/dashboard
   - Select your `VALabs-customer-portal` project

2. **Navigate to Settings**
   - Click on the "Settings" tab
   - Click on "Environment Variables" in the sidebar

3. **Add Each Variable**
   - Click "Add New"
   - Enter the variable name (e.g., `NEXT_PUBLIC_SUPABASE_URL`)
   - Enter the variable value
   - Select environments: `Production`, `Preview`, `Development`
   - Click "Save"

4. **Repeat for All Variables**
   - Add all the variables listed above
   - Make sure to select all environments for each

### **Method 2: Vercel CLI**

```bash
# Install Vercel CLI if you haven't
npm i -g vercel

# Login to Vercel
vercel login

# Set environment variables
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add SUPABASE_SERVICE_KEY production
vercel env add RETELL_API_KEY production
vercel env add NEXT_PUBLIC_RETELL_API_KEY production
vercel env add RETELL_LLM_ID production
vercel env add DEFAULT_ORGANIZATION_ID production
```

## 🔍 **Verify Environment Variables**

After setting the variables, you can verify them:

1. **In Vercel Dashboard**
   - Go to Settings > Environment Variables
   - You should see all 7 variables listed

2. **Test Deployment**
   - Trigger a new deployment by pushing to main
   - Check the deployment logs for any missing variable errors

## 🚨 **Common Issues & Solutions**

### **Issue 1: Missing Environment Variables**
```
Error: NEXT_PUBLIC_SUPABASE_URL is required
```
**Solution**: Make sure all required variables are set in Vercel

### **Issue 2: Build Errors**
```
Module not found: Can't resolve '@supabase/auth-helpers-nextjs'
```
**Solution**: This should be fixed now, but if it persists, check for any remaining old imports

### **Issue 3: Runtime Errors**
```
RetellAI client not initialized
```
**Solution**: Ensure `RETELL_API_KEY` is set correctly

## 🎯 **Quick Setup Checklist**

- [ ] Set `NEXT_PUBLIC_SUPABASE_URL`
- [ ] Set `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- [ ] Set `SUPABASE_SERVICE_KEY`
- [ ] Set `RETELL_API_KEY`
- [ ] Set `NEXT_PUBLIC_RETELL_API_KEY`
- [ ] Set `RETELL_LLM_ID`
- [ ] Set `DEFAULT_ORGANIZATION_ID`
- [ ] Trigger new deployment
- [ ] Check deployment logs
- [ ] Test the application

## 🔄 **After Setting Variables**

1. **Trigger New Deployment**
   ```bash
   git commit --allow-empty -m "trigger deployment"
   git push origin main
   ```

2. **Monitor Deployment**
   - Watch the Vercel dashboard for deployment status
   - Check build logs for any remaining errors

3. **Test Application**
   - Visit your deployed URL
   - Test agent creation
   - Verify organization context works

## 📞 **If Still Having Issues**

If the deployment still fails after setting all environment variables:

1. **Check Vercel Function Logs**
   - Go to Vercel Dashboard > Functions
   - Look for error logs

2. **Check Build Logs**
   - Look for specific error messages
   - Common issues: TypeScript errors, missing dependencies

3. **Test Locally**
   ```bash
   npm run build
   npm start
   ```

The most likely issue is missing environment variables in Vercel. Set all the variables listed above and trigger a new deployment!
