# Multi-Tenant Architecture Strategy

## Overview
VALabs Customer Portal is a multi-tenant SaaS platform where:
- **VALabs** = Service provider with one RetellAI account
- **Organizations** = Your customers (AFLAC, Company B, Company C, etc.)
- **Users** = Employees within each customer organization
- **Agents** = AI agents belonging to each customer organization

## Data Model

### Core Entities

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Organizations │    │ Organization     │    │ Organization    │
│   (Customers)   │◄──►│ Members          │◄──►│ Agents          │
│                 │    │ (User Access)    │    │ (AI Agents)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Billing/Plans   │    │ Auth Users       │    │ Calls           │
│ (Subscription)  │    │ (Supabase Auth)  │    │ (Call Data)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Tenant Isolation Strategy

#### **Option A: Row-Level Security (Current - Recommended)**
- ✅ Single database, multiple tenants
- ✅ Data isolation via RLS policies
- ✅ Cost-effective for SaaS
- ✅ Easier to manage and backup

#### **Option B: Schema-per-Tenant**
- ❌ Complex to manage
- ❌ Higher operational overhead
- ❌ Not recommended for your use case

#### **Option C: Database-per-Tenant**
- ❌ Very expensive
- ❌ Complex deployment
- ❌ Overkill for voice analytics

## Implementation Strategy

### Phase 1: Enhanced Multi-Tenant Foundation

1. **Organization Management**
   - Organization signup/onboarding flow
   - Billing integration (Stripe/similar)
   - Organization settings and branding
   - Usage limits and quotas

2. **User Management**
   - Invitation system for organization members
   - Role-based access control (Admin, Member, Viewer)
   - User onboarding within organizations

3. **Agent Management**
   - Organization-scoped agent creation
   - Agent sharing within organization
   - Agent usage analytics per organization

### Phase 2: Advanced Features

1. **Billing & Quotas**
   - Usage-based billing (calls, minutes, agents)
   - Plan limits enforcement
   - Overage handling

2. **Advanced Analytics**
   - Organization-level dashboards
   - Cross-agent analytics
   - Custom reporting

3. **Enterprise Features**
   - SSO integration
   - Advanced security controls
   - Custom branding

## RetellAI Integration Strategy

### Single RetellAI Account Approach

```
VALabs RetellAI Account
├── Agent_001 (AFLAC_CustomerService)
├── Agent_002 (AFLAC_Sales)
├── Agent_003 (CompanyB_Support)
├── Agent_004 (CompanyB_Billing)
└── Agent_005 (CompanyC_General)
```

**Benefits:**
- ✅ Single billing relationship with RetellAI
- ✅ Centralized management
- ✅ Cost optimization through volume
- ✅ Easier integration maintenance

**Naming Convention:**
- `{OrgName}_{Purpose}_{Version}` (e.g., "AFLAC_CustomerService_v1")
- Include organization identifier in agent metadata
- Use webhook routing to associate calls with correct organization

### Webhook Routing Strategy

```typescript
// Webhook handler
export async function POST(request: Request) {
  const payload = await request.json();
  
  // Extract organization from agent naming convention
  const agentId = payload.agent_id;
  const orgAgent = await getOrganizationAgent(agentId);
  
  if (!orgAgent) {
    throw new Error(`Unknown agent: ${agentId}`);
  }
  
  // Route call data to correct organization
  await insertCall({
    ...payload,
    organization_id: orgAgent.organization_id,
    organization_agent_id: orgAgent.id
  });
}
```

## Security Considerations

### Data Isolation
- ✅ RLS policies ensure tenant data separation
- ✅ API endpoints validate organization membership
- ✅ Frontend components respect organization context

### Access Control
- ✅ JWT tokens include organization context
- ✅ Middleware validates organization access
- ✅ Role-based permissions within organizations

### Compliance
- GDPR: Data deletion per organization
- SOC2: Audit trails per tenant
- HIPAA: Enhanced security for healthcare clients

## Scaling Considerations

### Database Performance
- Partition large tables by organization_id
- Optimize indexes for multi-tenant queries
- Monitor query performance per tenant

### Application Performance
- Cache organization data
- Implement rate limiting per organization
- Monitor resource usage per tenant

### Cost Management
- Track usage per organization
- Implement billing automation
- Monitor RetellAI costs vs. customer revenue

## Migration Strategy

### Immediate Actions
1. Create organization onboarding flow
2. Implement proper agent naming in RetellAI
3. Add organization context to all API calls
4. Update frontend to show organization branding

### Short-term (1-2 weeks)
1. User invitation system
2. Organization settings management
3. Enhanced RLS policies
4. Billing integration foundation

### Medium-term (1-2 months)
1. Advanced analytics per organization
2. Usage quotas and limits
3. Enterprise features (SSO, etc.)
4. Advanced security controls

## Success Metrics

### Technical
- Zero data leakage between organizations
- Sub-100ms query performance
- 99.9% uptime per organization

### Business
- Customer acquisition cost
- Monthly recurring revenue per organization
- Customer satisfaction scores
- Churn rate by organization size

## Next Steps

1. **Review current RLS policies** - Ensure complete data isolation
2. **Create organization onboarding flow** - New customer signup
3. **Implement agent naming convention** - RetellAI agent management
4. **Add organization context** - All API endpoints and UI
5. **Plan billing integration** - Revenue model implementation
