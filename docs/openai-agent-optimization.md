# OpenAI Agent Builder Integration

This document describes the OpenAI integration for optimizing agent configurations in the VALabs Customer Portal agent builder.

## Overview

The OpenAI integration enhances the agent creation process by using GPT-4o to analyze and optimize agent configurations based on the comprehensive data collected throughout the 7-step wizard process. This ensures that each agent is configured with the best possible prompts, behavior settings, and conversation flows for voice-based customer service.

## Architecture

### Components

1. **OpenAI Agent Optimizer** (`lib/openai-agent-optimizer.ts`)
   - Data bundling utilities
   - Pre-prompt templates
   - Response validation
   - Type definitions

2. **OpenAI API Route** (`app/api/openai/generate-agent-config/route.ts`)
   - Handles OpenAI API calls
   - Error handling and fallback logic
   - Usage tracking and logging

3. **Enhanced Agent Creation Step** (`components/agent-builder/AgentCreationStep.tsx`)
   - Integrated optimization step
   - Real-time progress tracking
   - Optimization results display

## Data Flow

```
Wizard Data Collection → OpenAI Optimization → RetellAI Agent Creation
```

### Step 1: Data Bundling
The system collects comprehensive data from all wizard steps:
- Selected template (system prompts, examples, knowledge topics)
- Basic information (name, description, purpose)
- Voice characteristics (ID, provider, gender, accent)
- Behavior configuration (personality traits, communication style)
- Knowledge base selections
- Training examples
- Organizational context

### Step 2: OpenAI Optimization
The bundled data is sent to OpenAI GPT-4o with a specialized pre-prompt that:
- Analyzes the agent's intended use case
- Optimizes prompts for voice conversations
- Enhances personality and communication style
- Integrates knowledge base topics naturally
- Adds appropriate error handling and escalation procedures
- Ensures compliance with insurance industry standards

### Step 3: Configuration Application
The optimized configuration is used to create:
- RetellAI LLM with enhanced prompts
- RetellAI Agent with optimized settings
- Organization registration
- Initial testing and validation

## Configuration

### Environment Variables

Add to your `.env.local` file:
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### OpenAI Settings

The integration uses:
- **Model**: GPT-4o (for highest quality optimization)
- **Temperature**: 0.3 (for consistent, structured output)
- **Max Tokens**: 4000 (sufficient for detailed configurations)
- **Response Format**: JSON object (structured output)

## Pre-Prompt Engineering

The system uses a comprehensive pre-prompt that instructs OpenAI to:

1. **Analyze Context**: Understand the agent's purpose, audience, and use case
2. **Optimize for Voice**: Design prompts specifically for voice interactions
3. **Industry Compliance**: Ensure insurance industry standards and regulations
4. **Conversation Flow**: Create natural, engaging conversation patterns
5. **Error Handling**: Include appropriate fallback and escalation procedures
6. **Performance Metrics**: Provide confidence scores and improvement suggestions

## Response Structure

OpenAI returns a structured JSON response:

```json
{
  "llm": {
    "general_prompt": "Optimized system prompt for voice conversations",
    "begin_message": "Natural, engaging opening message",
    "model": "gpt-4o-mini",
    "model_temperature": 0.7
  },
  "agent": {
    "agent_name": "Optimized agent name",
    "voice_id": "preserved_voice_id",
    "language": "en-US"
  },
  "metadata": {
    "optimization_notes": "Explanation of key optimizations",
    "suggested_improvements": ["List of recommendations"],
    "confidence_score": 0.95
  }
}
```

## Error Handling

The system includes robust error handling:

1. **OpenAI API Failures**: Falls back to original configuration
2. **Invalid Responses**: Validates structure and falls back if needed
3. **Rate Limiting**: Handles rate limits with appropriate error messages
4. **Network Issues**: Graceful degradation with user feedback

## User Experience

### Optimization Step
- Real-time progress indicator
- Brain icon with pulse animation during optimization
- Success notifications with optimization insights
- Confidence score visualization

### Results Display
- Optimization notes explaining key improvements
- Confidence score with visual progress bar
- Suggested improvements for future iterations
- Fallback indicators when optimization fails

## Benefits

1. **Enhanced Performance**: AI-optimized prompts perform better in voice conversations
2. **Industry Compliance**: Automatic inclusion of insurance industry best practices
3. **Consistency**: Standardized optimization across all agents
4. **Learning**: Continuous improvement through optimization insights
5. **Fallback Safety**: Always functional even if optimization fails

## Usage

The optimization is automatically triggered during agent creation:

1. User completes all wizard steps
2. Clicks "Create My AI Agent"
3. System bundles wizard data
4. Sends to OpenAI for optimization
5. Creates agent with optimized configuration
6. Displays results and insights

## Monitoring

The system logs:
- Optimization requests and responses
- API usage statistics (tokens, costs)
- Fallback occurrences
- Confidence scores and performance metrics

## Future Enhancements

1. **A/B Testing**: Compare optimized vs. non-optimized agents
2. **Learning Loop**: Use performance data to improve pre-prompts
3. **Custom Models**: Fine-tuned models for specific use cases
4. **Batch Optimization**: Optimize multiple agents simultaneously
5. **Performance Analytics**: Track optimization impact on agent performance

## Troubleshooting

### Common Issues

1. **Missing API Key**: Ensure `OPENAI_API_KEY` is set in environment
2. **Rate Limits**: Monitor usage and implement backoff strategies
3. **Invalid Responses**: Check pre-prompt and response validation
4. **Network Timeouts**: Implement retry logic with exponential backoff

### Debugging

Enable detailed logging by checking browser console and server logs for:
- OpenAI request/response data
- Optimization results
- Fallback triggers
- Error messages and stack traces
