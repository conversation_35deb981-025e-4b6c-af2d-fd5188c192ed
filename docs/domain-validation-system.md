# Domain Validation and Duplicate Prevention System

## Overview

This document describes the enhanced domain-based authentication system that prevents duplicate domains and ensures consistent domain validation across the application.

## Key Features

### 1. Domain Normalization
- **Automatic normalization**: All domains are automatically normalized on insert/update
- **Case-insensitive**: Domains are converted to lowercase
- **www prefix removal**: `www.example.com` becomes `example.com`
- **Whitespace trimming**: Leading/trailing spaces are removed

### 2. Duplicate Prevention
- **Database-level constraints**: Unique constraint on normalized domains
- **API-level validation**: Pre-validation before database operations
- **Real-time checking**: Frontend validation with immediate feedback
- **Cross-organization prevention**: No domain can be registered to multiple organizations

### 3. Format Validation
- **RFC-compliant validation**: Domains must follow standard format rules
- **Length limits**: Maximum 253 characters, minimum 4 characters
- **Character restrictions**: Only alphanumeric, hyphens, and dots allowed
- **Structure validation**: Must have at least one dot, proper label format

## Database Functions

### Core Functions

#### `normalize_domain(domain_input text)`
Normalizes a domain by:
- Converting to lowercase
- Trimming whitespace
- Removing www prefix

#### `is_valid_domain_format(domain_input text)`
Validates domain format according to RFC standards.

#### `domain_already_exists(domain_input text, exclude_org_id uuid)`
Checks if a domain already exists, optionally excluding a specific organization.

#### `validate_domain_for_creation(domain_input text, organization_id uuid, exclude_domain_id uuid)`
Comprehensive validation function that:
- Normalizes the domain
- Validates format
- Checks for duplicates
- Returns detailed validation results

### Triggers

#### `normalize_domain_trigger`
Automatically normalizes domains before insert/update operations.

#### `log_domain_changes_trigger`
Logs all domain changes for audit purposes.

## API Endpoints

### `/api/admin/domains/validate`
**POST** - Validates domain for creation/update
```json
{
  "domain": "example.com",
  "organizationId": "uuid",
  "excludeDomainId": "uuid"
}
```

**Response:**
```json
{
  "valid": true,
  "domain": "example.com",
  "message": "Domain is available for registration"
}
```

### `/api/admin/domains/check-availability`
**POST** - Quick domain availability check
```json
{
  "domain": "example.com"
}
```

**Response:**
```json
{
  "available": true,
  "domain": "example.com",
  "message": "Domain is available"
}
```

## Frontend Integration

### Organization Creator Component
- **Real-time validation**: Domains are validated as user types
- **Visual feedback**: Green checkmark for valid, red X for invalid
- **Error messages**: Detailed error descriptions
- **Duplicate prevention**: Form submission blocked if validation errors exist

### Validation States
- **Loading**: Spinner while validating
- **Valid**: Green border and checkmark
- **Invalid**: Red border and X with error message
- **Empty**: Default state

## Error Handling

### Common Error Types

#### `invalid_format`
Domain format doesn't meet RFC standards.

#### `domain_exists`
Domain is already registered to another organization.

#### `validation_error`
Server-side validation failed.

#### `server_error`
Unexpected server error occurred.

## Migration Files

### `20250131120000_add_domain_based_auth.sql`
- Creates organization_domains table
- Adds core validation functions
- Sets up RLS policies

### `20250131130000_add_domain_normalization_trigger.sql`
- Adds normalization trigger
- Creates audit logging
- Adds database constraints

## Testing

### Test Coverage
- Domain normalization consistency
- Format validation (valid/invalid cases)
- Duplicate prevention across organizations
- Case-insensitive duplicate detection
- API endpoint functionality
- Database constraint enforcement

### Test Files
- `tests/domain-validation.test.ts` - Comprehensive test suite

## Best Practices

### For Developers

1. **Always use API validation**: Don't rely solely on frontend validation
2. **Handle all error cases**: Provide meaningful error messages
3. **Test edge cases**: Include various domain formats in testing
4. **Monitor audit logs**: Track domain changes for security

### For Administrators

1. **Review domain requests**: Verify domain ownership before approval
2. **Monitor duplicate attempts**: Watch for suspicious domain registration patterns
3. **Regular audits**: Review organization domain assignments periodically

## Security Considerations

1. **Domain verification**: Implement DNS/email verification for production
2. **Rate limiting**: Prevent abuse of validation endpoints
3. **Audit logging**: All domain changes are logged with user attribution
4. **Access control**: Only organization admins can manage domains

## Future Enhancements

1. **DNS verification**: Automatic domain ownership verification
2. **Subdomain support**: Allow subdomains of verified domains
3. **Domain expiration**: Automatic cleanup of unused domains
4. **Bulk operations**: Import/export domain lists
5. **Advanced validation**: Check against domain blacklists
