"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Mail, ArrowLeft, CheckCircle, XCircle } from "lucide-react";

export default function CreateAccountPage() {
  const [step, setStep] = useState<'email' | 'authorized' | 'unauthorized'>('email');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const [organizationName, setOrganizationName] = useState('');
  const router = useRouter();

  const checkDomainAuthorization = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setMessage({ type: 'error', text: 'Please enter your email address' });
      return;
    }

    const emailDomain = email.split('@')[1];
    if (!emailDomain) {
      setMessage({ type: 'error', text: 'Please enter a valid email address' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      // Check if domain is authorized
      const response = await fetch('/api/auth/check-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      const result = await response.json();

      if (response.ok && result.authorized) {
        setOrganizationName(result.organizationName);
        setStep('authorized');
      } else {
        setStep('unauthorized');
      }
    } catch (err: any) {
      setMessage({ type: 'error', text: 'Failed to check domain authorization' });
    } finally {
      setLoading(false);
    }
  };

  const sendMagicLink = async () => {
    setLoading(true);
    setMessage(null);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: email.trim(),
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setMessage({ type: 'error', text: error.message });
      } else {
        setMessage({ 
          type: 'success', 
          text: 'Check your email for the magic link to create your account!' 
        });
      }
    } catch (err: any) {
      setMessage({ type: 'error', text: 'Failed to send magic link' });
    } finally {
      setLoading(false);
    }
  };

  if (step === 'email') {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardHeader className="text-center">
            <CardTitle className="text-white text-2xl">Create Account</CardTitle>
            <p className="text-gray-400">Enter your email to check if your organization is authorized</p>
          </CardHeader>
          <CardContent>
            <form onSubmit={checkDomainAuthorization} className="space-y-4">
              {message && (
                <Alert className="border-red-800 bg-red-900/20">
                  <AlertDescription className="text-red-400">
                    {message.text}
                  </AlertDescription>
                </Alert>
              )}

              <div>
                <label className="text-sm text-gray-400 mb-2 block">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your work email"
                  className="bg-gray-800 border-gray-700 text-white"
                  required
                />
              </div>

              <Button 
                type="submit" 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Check Authorization
              </Button>

              <Button 
                type="button"
                variant="ghost"
                onClick={() => router.push('/login')}
                className="w-full text-gray-400"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Login
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === 'authorized') {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardHeader className="text-center">
            <CardTitle className="text-white text-2xl flex items-center justify-center gap-2">
              <CheckCircle className="h-6 w-6 text-green-500" />
              Domain Authorized
            </CardTitle>
            <p className="text-gray-400">Your organization is authorized to create accounts</p>
          </CardHeader>
          <CardContent className="space-y-4">
            {message && (
              <Alert className={message.type === 'error' ? 'border-red-800 bg-red-900/20' : 'border-green-800 bg-green-900/20'}>
                <AlertDescription className={message.type === 'error' ? 'text-red-400' : 'text-green-400'}>
                  {message.text}
                </AlertDescription>
              </Alert>
            )}

            <div className="bg-gray-800 p-4 rounded-lg">
              <p className="text-sm text-gray-400 mb-1">Organization:</p>
              <p className="text-white font-medium">{organizationName}</p>
              <p className="text-sm text-gray-400 mt-2 mb-1">Email:</p>
              <p className="text-white">{email}</p>
            </div>

            <Button 
              onClick={sendMagicLink}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Mail className="h-4 w-4 mr-2" />
              )}
              Send Me a Magic Link
            </Button>

            <Button 
              type="button"
              variant="ghost"
              onClick={() => setStep('email')}
              className="w-full text-gray-400"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === 'unauthorized') {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardHeader className="text-center">
            <CardTitle className="text-white text-2xl flex items-center justify-center gap-2">
              <XCircle className="h-6 w-6 text-red-500" />
              Domain Not Authorized
            </CardTitle>
            <p className="text-gray-400">Your organization is not currently authorized</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert className="border-yellow-800 bg-yellow-900/20">
              <AlertDescription className="text-yellow-400">
                The domain for <strong>{email}</strong> is not currently authorized to create accounts. 
                Please contact your administrator or our support team.
              </AlertDescription>
            </Alert>

            <div className="bg-gray-800 p-4 rounded-lg">
              <p className="text-sm text-gray-400 mb-2">Need access?</p>
              <p className="text-white text-sm">
                Contact your organization's administrator or reach out to our support team 
                to request access for your domain.
              </p>
            </div>

            <Button 
              type="button"
              variant="ghost"
              onClick={() => setStep('email')}
              className="w-full text-gray-400"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Try Different Email
            </Button>

            <Button 
              type="button"
              variant="outline"
              onClick={() => router.push('/login')}
              className="w-full"
            >
              Back to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}
