"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileText, Trash2, Download, Upload } from "lucide-react"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"

interface KnowledgeBaseSource {
  source_id: string
  file_url: string
  filename: string
  type: string
  file_size: number
}

interface KnowledgeBase {
  knowledge_base_id: string
  knowledge_base_name: string
  knowledge_base_sources: KnowledgeBaseSource[]
  enable_auto_refresh: boolean
  status: string
  user_modified_timestamp: number
}

export default function Knowledge() {
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchKnowledgeBase = async () => {
      try {
        setError(null)
        
        // Fetch all knowledge bases
        const response = await fetch('/api/list-knowledge-bases')
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch knowledge bases')
        }
        
        const knowledgeBases = await response.json()
        
        // Find the Optional Employee Benefits knowledge base
        const targetKB = knowledgeBases.find(
          (kb: KnowledgeBase) => kb.knowledge_base_name === "Optional Employee Benefits"
        )

        if (!targetKB) {
          throw new Error('Knowledge base not found')
        }

        setKnowledgeBase(targetKB)
      } catch (error) {
        console.error('Error:', error)
        setError(error instanceof Error ? error.message : 'An unknown error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchKnowledgeBase()
  }, [])

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Byte'
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)).toString())
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i]
  }

  if (loading) {
    return <div className="p-8">Loading knowledge base...</div>
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-red-500">Error: {error}</div>
      </div>
    )
  }

  return (
    <div className="p-8 max-w-[1600px] mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-semibold">Knowledge Base</h1>
          <p className="text-muted-foreground mt-1">
            {knowledgeBase?.knowledge_base_name}
          </p>
        </div>
        <Button>
          <Upload className="h-4 w-4 mr-2" />
          Upload Document
        </Button>
      </div>

      <Card className="p-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Size</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {knowledgeBase?.knowledge_base_sources.map((source) => (
              <TableRow key={source.source_id}>
                <TableCell className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-500" />
                  {source.filename}
                </TableCell>
                <TableCell className="capitalize">{source.type}</TableCell>
                <TableCell>{formatFileSize(source.file_size)}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
} 