"use client";

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { User, Shield, Mail, Calendar, RefreshCw } from "lucide-react";

export default function AuthStatus() {
  const [session, setSession] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const checkAuth = async () => {
    setLoading(true);
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      console.log('Auth status check:', { session, error });
      
      setSession(session);
      setUser(session?.user || null);
    } catch (err) {
      console.error('Auth check error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session);
      setSession(session);
      setUser(session?.user || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
  };

  const handleSignIn = async () => {
    const { error } = await supabase.auth.signInWithOtp({
      email: '<EMAIL>',
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });

    if (error) {
      console.error('Sign in error:', error);
    } else {
      alert('Check your email for the magic link!');
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Authentication Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={checkAuth} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            {user ? (
              <Button onClick={handleSignOut} variant="destructive" size="sm">
                Sign Out
              </Button>
            ) : (
              <Button onClick={handleSignIn} size="sm">
                Send Magic Link
              </Button>
            )}
          </div>

          {loading ? (
            <Alert>
              <AlertDescription>Checking authentication status...</AlertDescription>
            </Alert>
          ) : user ? (
            <div className="space-y-4">
              <Alert className="border-green-800 bg-green-900/20">
                <User className="h-4 w-4" />
                <AlertDescription className="text-green-400">
                  ✅ You are authenticated!
                </AlertDescription>
              </Alert>

              <div className="bg-gray-800 p-4 rounded-lg space-y-3">
                <h3 className="text-white font-medium">User Information:</h3>
                
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">Email:</span>
                    <span className="text-white">{user.email}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">User ID:</span>
                    <span className="text-white font-mono text-xs">{user.id}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">Created:</span>
                    <span className="text-white">{new Date(user.created_at).toLocaleString()}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">Last Sign In:</span>
                    <span className="text-white">
                      {user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-2">Session Information:</h3>
                <pre className="text-xs text-gray-300 overflow-auto">
                  {JSON.stringify(session, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <Alert className="border-red-800 bg-red-900/20">
              <AlertDescription className="text-red-400">
                ❌ You are not authenticated. Click "Send Magic Link" to sign in.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
