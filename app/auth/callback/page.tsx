"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the code from URL parameters
        const code = searchParams.get('code');
        const error_code = searchParams.get('error_code');
        const error_description = searchParams.get('error_description');

        // Handle error cases
        if (error_code) {
          setError(error_description || 'Authentication failed');
          setLoading(false);
          return;
        }

        if (!code) {
          setError('No authentication code provided');
          setLoading(false);
          return;
        }

        // Exchange the code for a session
        const { data, error: authError } = await supabase.auth.exchangeCodeForSession(code);

        if (authError) {
          console.error('Auth callback error:', authError);
          setError(authError.message);
          setLoading(false);
          return;
        }

        if (data.user) {
          // Try to ensure user is assigned to organization
          try {
            const response = await fetch('/api/auth/ensure-organization-assignment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ userId: data.user.id, email: data.user.email }),
            });

            if (!response.ok) {
              console.warn('Failed to ensure organization assignment, but continuing...');
            }
          } catch (err) {
            console.warn('Organization assignment check failed, but continuing...', err);
          }

          setSuccess(true);
          setLoading(false);

          // Wait a moment to show success, then redirect
          setTimeout(() => {
            router.push('/');
          }, 2000);
        } else {
          setError('Authentication failed - no user data received');
          setLoading(false);
        }

      } catch (err) {
        console.error('Callback handling error:', err);
        setError('An unexpected error occurred during authentication');
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [searchParams, router]);

  const handleRetry = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader className="text-center">
          <CardTitle className="text-white">
            {loading && 'Signing you in...'}
            {success && 'Welcome!'}
            {error && 'Authentication Error'}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          {loading && (
            <div className="space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              <p className="text-gray-400">
                Processing your authentication...
              </p>
            </div>
          )}

          {success && (
            <div className="space-y-4">
              <CheckCircle className="h-8 w-8 mx-auto text-green-500" />
              <div>
                <p className="text-green-400 font-medium">
                  Successfully signed in!
                </p>
                <p className="text-gray-400 text-sm mt-1">
                  Redirecting you to the dashboard...
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="space-y-4">
              <Alert className="border-red-800 bg-red-900/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">
                  {error}
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <p className="text-gray-400 text-sm">
                  Please try signing in again or contact support if the problem persists.
                </p>
                <Button onClick={handleRetry} className="w-full">
                  Return to Sign In
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
