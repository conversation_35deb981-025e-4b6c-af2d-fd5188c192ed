import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const token = searchParams.get('token')
  const type = searchParams.get('type')
  const next = searchParams.get('next') ?? '/'

  console.log('Auth callback route called with:', { code: !!code, token: !!token, type, origin })

  if (code || (token && type)) {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )

    let error = null;
    let result = null;

    if (code) {
      // Handle PKCE flow
      console.log('Processing PKCE flow with code')
      result = await supabase.auth.exchangeCodeForSession(code)
      error = result.error
    } else if (token && type) {
      // Handle magic link flow
      console.log('Processing magic link flow with token')
      result = await supabase.auth.verifyOtp({
        token_hash: token,
        type: 'magiclink'
      })
      error = result.error
    }

    if (error) {
      console.error('Auth callback error:', error)
      return NextResponse.redirect(`${origin}/auth/auth-code-error`)
    }

    if (result?.data?.user) {
      console.log('Auth successful, redirecting to:', next)
      const forwardedHost = request.headers.get('x-forwarded-host')
      const isLocalEnv = process.env.NODE_ENV === 'development'

      if (isLocalEnv) {
        return NextResponse.redirect(`${origin}${next}`)
      } else if (forwardedHost) {
        return NextResponse.redirect(`https://${forwardedHost}${next}`)
      } else {
        return NextResponse.redirect(`${origin}${next}`)
      }
    }
  }

  console.log('No valid auth data found, redirecting to error page')
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
