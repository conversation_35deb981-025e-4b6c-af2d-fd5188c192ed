"use client";

import { useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function TestMagicLink() {
  const [email, setEmail] = useState('<EMAIL>');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const sendMagicLink = async () => {
    setLoading(true);
    setMessage(null);
    setError(null);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        setError(error.message);
      } else {
        setMessage(`Magic link sent to ${email}! Check your inbox.`);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send magic link');
    } finally {
      setLoading(false);
    }
  };

  const checkSession = async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    console.log('Current session:', session);
    console.log('Session error:', error);
    
    if (session) {
      setMessage(`Logged in as: ${session.user.email}`);
    } else {
      setMessage('No active session');
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setMessage('Signed out');
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white">Magic Link Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert className="border-red-800 bg-red-900/20">
              <AlertDescription className="text-red-400">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {message && (
            <Alert className="border-green-800 bg-green-900/20">
              <AlertDescription className="text-green-400">
                {message}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <label className="text-white text-sm">Email:</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          <div className="space-y-2">
            <Button 
              onClick={sendMagicLink} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Sending...' : 'Send Magic Link'}
            </Button>

            <Button 
              onClick={checkSession} 
              variant="outline"
              className="w-full"
            >
              Check Session
            </Button>

            <Button 
              onClick={signOut} 
              variant="outline"
              className="w-full"
            >
              Sign Out
            </Button>
          </div>

          <div className="text-xs text-gray-400 space-y-1">
            <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
            <p><strong>Redirect URL:</strong> {typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : 'N/A'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
