"use client";

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function AuthDebug() {
  const [session, setSession] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session);
      setSession(session);
      setUser(session?.user || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAuth = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      console.log('Current session:', session);
      console.log('Session error:', error);
      
      setSession(session);
      setUser(session?.user || null);
      setError(error?.message || null);
    } catch (err: any) {
      console.error('Auth check error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const sendMagicLink = async () => {
    setLoading(true);
    setError(null);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: '<EMAIL>',
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        setError(error.message);
      } else {
        alert('Magic link sent! Check your email.');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send magic link');
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setSession(null);
    setUser(null);
  };

  const testAPI = async () => {
    try {
      const response = await fetch('/api/admin/organizations');
      const data = await response.json();
      console.log('API Response:', data);
      
      if (!response.ok) {
        setError(`API Error: ${data.error || 'Unknown error'}`);
      } else {
        alert('API call successful! Check console for details.');
      }
    } catch (err: any) {
      setError(`API Error: ${err.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white">Authentication Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert className="border-red-800 bg-red-900/20">
              <AlertDescription className="text-red-400">
                {error}
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button onClick={sendMagicLink} disabled={loading}>
              Send Magic Link
            </Button>
            <Button onClick={checkAuth} variant="outline">
              Check Auth
            </Button>
            <Button onClick={signOut} variant="outline">
              Sign Out
            </Button>
            <Button onClick={testAPI} variant="outline">
              Test API
            </Button>
          </div>

          <div className="space-y-4 text-sm">
            <div className="bg-gray-800 p-4 rounded">
              <h3 className="text-white font-semibold mb-2">Session Status:</h3>
              <pre className="text-gray-300 overflow-auto">
                {loading ? 'Loading...' : session ? JSON.stringify(session, null, 2) : 'No session'}
              </pre>
            </div>

            <div className="bg-gray-800 p-4 rounded">
              <h3 className="text-white font-semibold mb-2">User Info:</h3>
              <pre className="text-gray-300 overflow-auto">
                {user ? JSON.stringify(user, null, 2) : 'No user'}
              </pre>
            </div>

            <div className="bg-gray-800 p-4 rounded">
              <h3 className="text-white font-semibold mb-2">Environment:</h3>
              <pre className="text-gray-300 overflow-auto">
                {JSON.stringify({
                  url: typeof window !== 'undefined' ? window.location.href : 'N/A',
                  origin: typeof window !== 'undefined' ? window.location.origin : 'N/A',
                  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
                  redirectUrl: typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : 'N/A'
                }, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
