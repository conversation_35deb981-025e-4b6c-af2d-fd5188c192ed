import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const token = searchParams.get('token')
  const type = searchParams.get('type')
  const next = searchParams.get('next') ?? '/'

  console.log('Verify endpoint called with:', { token, type, next })

  if (token && type) {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Server component limitation
            }
          },
        },
      }
    )

    try {
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: type as any
      })

      console.log('Verification result:', { data: !!data, error })

      if (!error && data.user) {
        // Redirect to success page
        return NextResponse.redirect(`${origin}/auth/success?next=${encodeURIComponent(next)}`)
      } else {
        console.error('Verification failed:', error)
        return NextResponse.redirect(`${origin}/auth/auth-code-error`)
      }
    } catch (err) {
      console.error('Verification exception:', err)
      return NextResponse.redirect(`${origin}/auth/auth-code-error`)
    }
  }

  // No token provided
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
