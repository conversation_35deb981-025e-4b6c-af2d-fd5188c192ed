"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertCircle, Mail } from "lucide-react";
import { useRouter } from 'next/navigation';

export default function AuthCodeError() {
  const router = useRouter();

  const handleRetry = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader className="text-center">
          <CardTitle className="text-white flex items-center justify-center gap-2">
            <AlertCircle className="h-6 w-6 text-red-500" />
            Authentication Error
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="border-red-800 bg-red-900/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-400">
              The authentication link appears to be invalid or has expired.
            </AlertDescription>
          </Alert>

          <div className="space-y-3 text-center">
            <div className="flex items-center justify-center gap-2 text-gray-400">
              <Mail className="h-4 w-4" />
              <span className="text-sm">Possible solutions:</span>
            </div>
            
            <ul className="text-sm text-gray-400 space-y-1 text-left">
              <li>• Check if the link in your email is complete</li>
              <li>• Try copying and pasting the entire URL</li>
              <li>• Request a new magic link</li>
              <li>• Make sure you're using the latest email</li>
            </ul>

            <Button onClick={handleRetry} className="w-full mt-4">
              Return to Sign In
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
