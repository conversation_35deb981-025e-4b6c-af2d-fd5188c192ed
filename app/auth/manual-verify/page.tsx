"use client";

import { useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { useRouter } from 'next/navigation';

export default function ManualVerify() {
  const [token, setToken] = useState('437a0152f5759fd7fec7cf6d07e81bd6cbe2873c595afc02f9061d7f');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleVerify = async () => {
    if (!token.trim()) {
      setError('Please enter a token');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Attempting to verify token:', token);

      // Try method 1: verifyOtp
      const { data, error: verifyError } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: 'magiclink'
      });

      console.log('VerifyOtp result:', { data, error: verifyError });

      if (verifyError) {
        // Try method 2: direct session check
        const { data: sessionData } = await supabase.auth.getSession();
        console.log('Session check:', sessionData);

        if (sessionData?.session) {
          setSuccess(true);
          setTimeout(() => router.push('/'), 2000);
          return;
        }

        throw verifyError;
      }

      if (data?.user) {
        setSuccess(true);
        setTimeout(() => router.push('/'), 2000);
      } else {
        setError('Verification failed - no user data');
      }

    } catch (err: any) {
      console.error('Verification error:', err);
      setError(err.message || 'Verification failed');
    } finally {
      setLoading(false);
    }
  };

  const handleServerVerify = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/auth/verify-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, type: 'magiclink' }),
      });

      const result = await response.json();
      console.log('Server verify result:', result);

      if (response.ok && result.success) {
        setSuccess(true);
        setTimeout(() => router.push('/'), 2000);
      } else {
        setError(result.error || 'Server verification failed');
      }
    } catch (err: any) {
      console.error('Server verification error:', err);
      setError('Server verification failed');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardContent className="text-center pt-6">
            <CheckCircle className="h-8 w-8 mx-auto text-green-500 mb-4" />
            <p className="text-green-400 font-medium">Successfully verified!</p>
            <p className="text-gray-400 text-sm mt-1">Redirecting...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white">Manual Token Verification</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert className="border-red-800 bg-red-900/20">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-400">
                {error}
              </AlertDescription>
            </Alert>
          )}

          <div>
            <label className="text-sm text-gray-400 mb-2 block">
              Token from magic link:
            </label>
            <Input
              value={token}
              onChange={(e) => setToken(e.target.value)}
              placeholder="Enter token here..."
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          <div className="space-y-2">
            <Button 
              onClick={handleVerify} 
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Verify Client-Side
            </Button>

            <Button 
              onClick={handleServerVerify} 
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Verify Server-Side
            </Button>
          </div>

          <p className="text-xs text-gray-500 text-center">
            This is a debug page to test token verification
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
