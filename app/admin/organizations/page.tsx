"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Building2, 
  Plus, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Users, 
  Globe,
  Settings,
  Trash2,
  Edit
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: string;
  status: string;
  billing_email?: string;
  max_agents: number;
  max_calls_per_month: number;
  created_at: string;
  organization_domains?: Array<{
    id: string;
    domain: string;
    verified: boolean;
    admin_approved: boolean;
    auto_assign_role: string;
  }>;
  organization_members?: Array<{
    id: string;
    role: string;
  }>;
}

interface DomainInput {
  domain: string;
  auto_assign_role: string;
  verified: boolean;
  admin_approved: boolean;
}

export default function AdminOrganizationsPage() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    plan: 'free',
    status: 'active',
    billing_email: '',
    max_agents: 3,
    max_calls_per_month: 1000,
    settings: '{}'
  });

  const [domains, setDomains] = useState<DomainInput[]>([
    { domain: '', auto_assign_role: 'member', verified: false, admin_approved: false }
  ]);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/organizations');
      if (!response.ok) throw new Error('Failed to fetch organizations');
      
      const data = await response.json();
      setOrganizations(data.organizations || []);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError('Failed to load organizations');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-generate slug from name
    if (field === 'name' && !formData.slug) {
      const slug = value.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      setFormData(prev => ({
        ...prev,
        slug
      }));
    }
  };

  const addDomain = () => {
    setDomains(prev => [
      ...prev,
      { domain: '', auto_assign_role: 'member', verified: false, admin_approved: false }
    ]);
  };

  const removeDomain = (index: number) => {
    setDomains(prev => prev.filter((_, i) => i !== index));
  };

  const updateDomain = (index: number, field: string, value: any) => {
    setDomains(prev => prev.map((domain, i) => 
      i === index ? { ...domain, [field]: value } : domain
    ));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreating(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate settings JSON
      let parsedSettings = {};
      if (formData.settings.trim()) {
        try {
          parsedSettings = JSON.parse(formData.settings);
        } catch {
          throw new Error('Invalid JSON in settings field');
        }
      }

      // Filter out empty domains
      const validDomains = domains.filter(d => d.domain.trim());

      const payload = {
        ...formData,
        settings: parsedSettings,
        domains: validDomains
      };

      const response = await fetch('/api/admin/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create organization');
      }

      setSuccess('Organization created successfully!');
      toast({
        title: "Success",
        description: "Organization created successfully",
      });

      // Reset form
      setFormData({
        name: '',
        slug: '',
        plan: 'free',
        status: 'active',
        billing_email: '',
        max_agents: 3,
        max_calls_per_month: 1000,
        settings: '{}'
      });
      setDomains([
        { domain: '', auto_assign_role: 'member', verified: false, admin_approved: false }
      ]);

      // Refresh organizations list
      fetchOrganizations();

    } catch (err: any) {
      console.error('Error creating organization:', err);
      setError(err.message || 'Failed to create organization');
      toast({
        title: "Error",
        description: err.message || 'Failed to create organization',
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Organization Administration</h1>
          <p className="text-muted-foreground mt-1">
            Create and manage organizations for the VALabs platform
          </p>
        </div>
      </div>

      <Tabs defaultValue="create" className="space-y-6">
        <TabsList>
          <TabsTrigger value="create">Create Organization</TabsTrigger>
          <TabsTrigger value="list">Manage Organizations</TabsTrigger>
        </TabsList>

        <TabsContent value="create">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Create New Organization
              </CardTitle>
              <CardDescription>
                Set up a new organization with domains and access controls
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert className="mb-6 border-red-800 bg-red-900/20">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-400">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="mb-6 border-green-800 bg-green-900/20">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription className="text-green-400">
                    {success}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Organization Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Acme Corporation"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="slug">Slug</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => handleInputChange('slug', e.target.value)}
                      placeholder="acme-corporation"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Auto-generated from name if left empty
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="plan">Plan</Label>
                    <Select value={formData.plan} onValueChange={(value) => handleInputChange('plan', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="free">Free</SelectItem>
                        <SelectItem value="starter">Starter</SelectItem>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="enterprise">Enterprise</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="suspended">Suspended</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="billing_email">Billing Email</Label>
                    <Input
                      id="billing_email"
                      type="email"
                      value={formData.billing_email}
                      onChange={(e) => handleInputChange('billing_email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {/* Limits */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="max_agents">Max Agents</Label>
                    <Input
                      id="max_agents"
                      type="number"
                      min="1"
                      value={formData.max_agents}
                      onChange={(e) => handleInputChange('max_agents', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="max_calls_per_month">Max Calls per Month</Label>
                    <Input
                      id="max_calls_per_month"
                      type="number"
                      min="1"
                      value={formData.max_calls_per_month}
                      onChange={(e) => handleInputChange('max_calls_per_month', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                {/* Settings */}
                <div>
                  <Label htmlFor="settings">Settings (JSON)</Label>
                  <Textarea
                    id="settings"
                    value={formData.settings}
                    onChange={(e) => handleInputChange('settings', e.target.value)}
                    placeholder='{"timezone": "America/New_York", "features": {"analytics": true}}'
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Optional JSON configuration for organization-specific settings
                  </p>
                </div>

                {/* Domains */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <Label>Authorized Domains</Label>
                    <Button type="button" onClick={addDomain} variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Domain
                    </Button>
                  </div>
                  
                  <div className="space-y-3">
                    {domains.map((domain, index) => (
                      <div key={index} className="grid grid-cols-12 gap-2 items-end">
                        <div className="col-span-4">
                          <Input
                            placeholder="company.com"
                            value={domain.domain}
                            onChange={(e) => updateDomain(index, 'domain', e.target.value)}
                          />
                        </div>
                        <div className="col-span-2">
                          <Select 
                            value={domain.auto_assign_role} 
                            onValueChange={(value) => updateDomain(index, 'auto_assign_role', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Admin</SelectItem>
                              <SelectItem value="member">Member</SelectItem>
                              <SelectItem value="viewer">Viewer</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="col-span-2 flex gap-2">
                          <label className="flex items-center space-x-1">
                            <input
                              type="checkbox"
                              checked={domain.verified}
                              onChange={(e) => updateDomain(index, 'verified', e.target.checked)}
                              className="rounded"
                            />
                            <span className="text-xs">Verified</span>
                          </label>
                        </div>
                        <div className="col-span-2 flex gap-2">
                          <label className="flex items-center space-x-1">
                            <input
                              type="checkbox"
                              checked={domain.admin_approved}
                              onChange={(e) => updateDomain(index, 'admin_approved', e.target.checked)}
                              className="rounded"
                            />
                            <span className="text-xs">Approved</span>
                          </label>
                        </div>
                        <div className="col-span-2">
                          {domains.length > 1 && (
                            <Button
                              type="button"
                              onClick={() => removeDomain(index)}
                              variant="outline"
                              size="sm"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Users with email addresses from these domains will be automatically assigned to this organization
                  </p>
                </div>

                <Button type="submit" disabled={creating} className="w-full">
                  {creating ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Creating Organization...
                    </>
                  ) : (
                    <>
                      <Building2 className="h-4 w-4 mr-2" />
                      Create Organization
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Existing Organizations
              </CardTitle>
              <CardDescription>
                View and manage existing organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  Loading organizations...
                </div>
              ) : organizations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No organizations found. Create your first organization above.
                </div>
              ) : (
                <div className="space-y-4">
                  {organizations.map((org) => (
                    <div key={org.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{org.name}</h3>
                            <Badge variant={org.status === 'active' ? 'default' : 'secondary'}>
                              {org.status}
                            </Badge>
                            <Badge variant="outline">{org.plan}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            Slug: {org.slug} • Created: {new Date(org.created_at).toLocaleDateString()}
                          </p>
                          <div className="flex items-center gap-4 text-sm">
                            <span className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {org.organization_members?.length || 0} members
                            </span>
                            <span className="flex items-center gap-1">
                              <Globe className="h-4 w-4" />
                              {org.organization_domains?.length || 0} domains
                            </span>
                          </div>
                          {org.organization_domains && org.organization_domains.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs text-muted-foreground mb-1">Domains:</p>
                              <div className="flex flex-wrap gap-1">
                                {org.organization_domains.map((domain) => (
                                  <Badge key={domain.id} variant="outline" className="text-xs">
                                    {domain.domain}
                                    {domain.verified && domain.admin_approved && (
                                      <CheckCircle className="h-3 w-3 ml-1 text-green-500" />
                                    )}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
