"use client"

import { Card } from "@/components/ui/card"
import { <PERSON> } from "lucide-react"

export default function InsightsPage() {
  return (
    <div className="p-8 max-w-[1600px] mx-auto">
      <div className="flex items-center gap-2 mb-8">
        <h1 className="text-2xl font-semibold">Intelligent Insights</h1>
        <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
          In Progress
        </span>
      </div>

      <Card className="p-6">
        <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
          <Brain className="h-12 w-12 mb-4" />
          <p className="text-lg mb-2">Intelligent Insights Coming Soon</p>
          <p className="text-sm">
            We're working on advanced analytics and AI-powered insights to help you better understand your calls.
          </p>
        </div>
      </Card>
    </div>
  )
} 