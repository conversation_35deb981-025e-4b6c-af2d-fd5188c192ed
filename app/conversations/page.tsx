'use client'

import { useState } from 'react'
import { searchTranscripts } from '@/lib/supabase/queries'

export default function ConversationsPage() {
  const [searchResults, setSearchResults] = useState<any[]>([])
  const organizationId = 'd0816f44-4576-4b6f-8c45-a80464f0a6c8'

  const handleSearch = async (query: string) => {
    if (!query.trim()) return
    const results = await searchTranscripts(organizationId, query)
    setSearchResults(results)
  }

  return (
    <div>
      <input 
        type="search" 
        placeholder="Search transcripts..."
        onChange={(e) => handleSearch(e.target.value)}
      />
      {/* Render search results */}
    </div>
  )
} 