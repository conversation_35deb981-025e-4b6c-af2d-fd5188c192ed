import { NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'
import { listAgents, getAgentDetails } from '@/lib/retell'

interface SyncResult {
  success: boolean
  summary: {
    total_retell_agents: number
    total_db_agents: number
    added: number
    updated: number
    removed: number
    errors: number
  }
  details: {
    added: Array<{ agent_id: string; name: string; organization_id: string }>
    updated: Array<{ agent_id: string; changes: string[] }>
    removed: Array<{ agent_id: string; reason: string }>
    errors: Array<{ agent_id: string; error: string }>
  }
}

// POST /api/sync/agents - Sync all agents with RetellAI
export async function POST(request: Request) {
  try {
    const { organization_id, dry_run = false } = await request.json()

    if (!organization_id) {
      return NextResponse.json(
        { error: 'organization_id is required' },
        { status: 400 }
      )
    }

    const result: SyncResult = {
      success: true,
      summary: {
        total_retell_agents: 0,
        total_db_agents: 0,
        added: 0,
        updated: 0,
        removed: 0,
        errors: 0
      },
      details: {
        added: [],
        updated: [],
        removed: [],
        errors: []
      }
    }

    // 1. Get all RetellAI agents
    const retellAgents = await listAgents()
    result.summary.total_retell_agents = retellAgents.length

    // 2. Get all organization agents from DB
    const { data: dbAgents, error: dbError } = await supabaseServiceRole
      .from('organization_agents')
      .select('*')
      .eq('organization_id', organization_id)

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`)
    }

    result.summary.total_db_agents = dbAgents?.length || 0

    // 3. Create maps for efficient comparison
    const retellAgentMap = new Map(retellAgents.map(agent => [agent.agent_id, agent]))
    const dbAgentMap = new Map((dbAgents || []).map(agent => [agent.agent_id, agent]))

    // 4. Find agents to add (in RetellAI but not in DB)
    for (const retellAgent of retellAgents) {
      if (!dbAgentMap.has(retellAgent.agent_id)) {
        try {
          if (!dry_run) {
            const { data: newAgent, error: insertError } = await supabaseServiceRole
              .from('organization_agents')
              .insert({
                organization_id,
                agent_id: retellAgent.agent_id,
                name: retellAgent.agent_name || `Agent ${retellAgent.agent_id}`,
                type: 'inbound_voice', // Default type, can be updated later
                status: 'active',
                settings: {
                  voice_id: retellAgent.voice_id,
                  language: retellAgent.language,
                  synced_at: new Date().toISOString(),
                  retell_data: retellAgent
                }
              })
              .select()
              .single()

            if (insertError) {
              throw new Error(insertError.message)
            }
          }

          result.details.added.push({
            agent_id: retellAgent.agent_id,
            name: retellAgent.agent_name || `Agent ${retellAgent.agent_id}`,
            organization_id
          })
          result.summary.added++
        } catch (error) {
          result.details.errors.push({
            agent_id: retellAgent.agent_id,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          result.summary.errors++
        }
      }
    }

    // 5. Find agents to update (in both, but data might be stale)
    for (const dbAgent of dbAgents || []) {
      const retellAgent = retellAgentMap.get(dbAgent.agent_id)
      if (retellAgent) {
        const changes: string[] = []
        const updates: any = {}

        // Check for name changes
        if (retellAgent.agent_name && retellAgent.agent_name !== dbAgent.name) {
          changes.push(`name: ${dbAgent.name} → ${retellAgent.agent_name}`)
          updates.name = retellAgent.agent_name
        }

        // Check for settings changes
        const currentRetellData = dbAgent.settings?.retell_data
        if (JSON.stringify(currentRetellData) !== JSON.stringify(retellAgent)) {
          changes.push('retell_data updated')
          updates.settings = {
            ...dbAgent.settings,
            retell_data: retellAgent,
            synced_at: new Date().toISOString()
          }
        }

        if (changes.length > 0) {
          try {
            if (!dry_run) {
              updates.updated_at = new Date().toISOString()
              
              const { error: updateError } = await supabaseServiceRole
                .from('organization_agents')
                .update(updates)
                .eq('id', dbAgent.id)

              if (updateError) {
                throw new Error(updateError.message)
              }
            }

            result.details.updated.push({
              agent_id: dbAgent.agent_id,
              changes
            })
            result.summary.updated++
          } catch (error) {
            result.details.errors.push({
              agent_id: dbAgent.agent_id,
              error: error instanceof Error ? error.message : 'Unknown error'
            })
            result.summary.errors++
          }
        }
      }
    }

    // 6. Find agents to remove (in DB but not in RetellAI)
    for (const dbAgent of dbAgents || []) {
      if (!retellAgentMap.has(dbAgent.agent_id)) {
        try {
          if (!dry_run) {
            // Don't delete, just mark as inactive
            const { error: updateError } = await supabaseServiceRole
              .from('organization_agents')
              .update({
                status: 'inactive',
                updated_at: new Date().toISOString(),
                settings: {
                  ...dbAgent.settings,
                  removed_from_retell_at: new Date().toISOString()
                }
              })
              .eq('id', dbAgent.id)

            if (updateError) {
              throw new Error(updateError.message)
            }
          }

          result.details.removed.push({
            agent_id: dbAgent.agent_id,
            reason: 'Agent no longer exists in RetellAI'
          })
          result.summary.removed++
        } catch (error) {
          result.details.errors.push({
            agent_id: dbAgent.agent_id,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          result.summary.errors++
        }
      }
    }

    // 7. Update sync metadata
    if (!dry_run && result.summary.errors === 0) {
      await supabaseServiceRole
        .from('organizations')
        .update({
          settings: {
            last_agent_sync: new Date().toISOString(),
            last_sync_summary: result.summary
          }
        })
        .eq('id', organization_id)
    }

    return NextResponse.json({
      ...result,
      dry_run,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in POST /api/sync/agents:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// GET /api/sync/agents - Get sync status
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const organization_id = searchParams.get('organization_id')

    if (!organization_id) {
      return NextResponse.json(
        { error: 'organization_id is required' },
        { status: 400 }
      )
    }

    // Get organization sync metadata
    const { data: org, error: orgError } = await supabaseServiceRole
      .from('organizations')
      .select('settings')
      .eq('id', organization_id)
      .single()

    if (orgError) {
      throw new Error(`Database error: ${orgError.message}`)
    }

    const lastSync = org?.settings?.last_agent_sync
    const lastSyncSummary = org?.settings?.last_sync_summary

    // Get current counts
    const [retellAgents, { data: dbAgents }] = await Promise.all([
      listAgents(),
      supabaseServiceRole
        .from('organization_agents')
        .select('agent_id, status')
        .eq('organization_id', organization_id)
    ])

    const activeDbAgents = dbAgents?.filter(agent => agent.status === 'active') || []
    const inactiveDbAgents = dbAgents?.filter(agent => agent.status === 'inactive') || []

    return NextResponse.json({
      organization_id,
      last_sync: lastSync,
      last_sync_summary: lastSyncSummary,
      current_status: {
        retell_agents: retellAgents.length,
        db_agents_active: activeDbAgents.length,
        db_agents_inactive: inactiveDbAgents.length,
        sync_needed: retellAgents.length !== activeDbAgents.length
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in GET /api/sync/agents:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
