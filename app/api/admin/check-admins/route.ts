import { NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

// GET /api/admin/check-admins - Check which users have admin roles
export async function GET() {
  try {
    // Get all admin users across all organizations
    const { data: adminMemberships, error } = await supabaseServiceRole
      .from('organization_members')
      .select(`
        user_id,
        role,
        joined_at,
        organization_id,
        organizations (
          id,
          name,
          slug
        )
      `)
      .eq('role', 'admin')
      .order('joined_at', { ascending: false })

    if (error) {
      console.error('Error fetching admin memberships:', error)
      return NextResponse.json(
        { error: 'Failed to fetch admin users' },
        { status: 500 }
      )
    }

    // Get user details for each admin
    const adminUsers = []
    for (const membership of adminMemberships || []) {
      const { data: user, error: userError } = await supabaseServiceRole.auth.admin.getUserById(membership.user_id)

      if (!userError && user) {
        adminUsers.push({
          ...membership,
          auth: {
            users: {
              id: user.user.id,
              email: user.user.email,
              created_at: user.user.created_at,
              last_sign_in_at: user.user.last_sign_in_at
            }
          }
        })
      }
    }

    // Format the response
    const formattedAdmins = adminUsers?.map(admin => ({
      userId: admin.user_id,
      email: admin.auth?.users?.email,
      role: admin.role,
      joinedAt: admin.joined_at,
      createdAt: admin.auth?.users?.created_at,
      lastSignInAt: admin.auth?.users?.last_sign_in_at,
      organization: {
        id: admin.organization_id,
        name: admin.organizations?.name,
        slug: admin.organizations?.slug
      }
    })) || []

    // Group by organization
    const adminsByOrg = formattedAdmins.reduce((acc, admin) => {
      const orgName = admin.organization.name || 'Unknown Organization'
      if (!acc[orgName]) {
        acc[orgName] = []
      }
      acc[orgName].push(admin)
      return acc
    }, {} as Record<string, typeof formattedAdmins>)

    return NextResponse.json({
      totalAdmins: formattedAdmins.length,
      adminUsers: formattedAdmins,
      adminsByOrganization: adminsByOrg,
      summary: Object.keys(adminsByOrg).map(orgName => ({
        organization: orgName,
        adminCount: adminsByOrg[orgName].length,
        adminEmails: adminsByOrg[orgName].map(admin => admin.email)
      }))
    })

  } catch (error) {
    console.error('Check admins error:', error)
    return NextResponse.json(
      { error: 'Failed to check admin users' },
      { status: 500 }
    )
  }
}

// POST /api/admin/check-admins - Make a user an admin (for development)
export async function POST(request: Request) {
  try {
    const { email, organizationId } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Find the user by email using auth admin API
    const { data: userList, error: userError } = await supabaseServiceRole.auth.admin.listUsers()

    if (userError) {
      console.error('Error listing users:', userError)
      return NextResponse.json(
        { error: 'Failed to search for user' },
        { status: 500 }
      )
    }

    const user = userList.users.find(u => u.email === email)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Use provided organization ID or default
    const targetOrgId = organizationId || process.env.DEFAULT_ORGANIZATION_ID || 'd0816f44-4576-4b6f-8c45-a80464f0a6c8'

    if (!targetOrgId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      )
    }

    // Update the user's role to admin
    const { data: updatedMembership, error: updateError } = await supabaseServiceRole
      .from('organization_members')
      .update({ role: 'admin' })
      .eq('user_id', user.id)
      .eq('organization_id', targetOrgId)
      .select()
      .single()

    if (updateError) {
      // If update failed, try to create the membership
      const { data: newMembership, error: insertError } = await supabaseServiceRole
        .from('organization_members')
        .insert({
          user_id: user.id,
          organization_id: targetOrgId,
          role: 'admin',
          joined_at: new Date().toISOString()
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error creating admin membership:', insertError)
        return NextResponse.json(
          { error: 'Failed to make user admin' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: `${email} is now an admin (new membership created)`,
        membership: newMembership
      })
    }

    return NextResponse.json({
      success: true,
      message: `${email} is now an admin (role updated)`,
      membership: updatedMembership
    })

  } catch (error) {
    console.error('Make admin error:', error)
    return NextResponse.json(
      { error: 'Failed to make user admin' },
      { status: 500 }
    )
  }
}
