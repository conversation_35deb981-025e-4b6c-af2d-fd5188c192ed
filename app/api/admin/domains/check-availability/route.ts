import { NextResponse } from 'next/server';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

// POST /api/admin/domains/check-availability - Check if domain is available
export async function POST(request: Request) {
  try {
    const { domain } = await request.json();

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }

    // Call the database function to check availability
    const { data, error } = await supabaseServiceRole.rpc(
      'check_domain_availability',
      { domain_input: domain.trim() }
    );

    if (error) {
      console.error('Domain availability check error:', error);
      return NextResponse.json(
        { 
          available: false,
          error: 'check_error',
          message: 'Unable to check domain availability. Please try again.',
          domain: domain.trim()
        },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Domain availability request error:', error);
    return NextResponse.json(
      { 
        available: false,
        error: 'server_error',
        message: 'Server error occurred. Please try again.',
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/domains/check-availability - Health check
export async function GET() {
  try {
    // Test database connection and function availability
    const { data, error } = await supabaseServiceRole.rpc(
      'check_domain_availability',
      { domain_input: 'test-domain-health-check.com' }
    );

    if (error) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'Domain availability check function not available',
          error: error.message
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      service: 'Domain Availability Check API',
      timestamp: new Date().toISOString(),
      functions_available: ['check_domain_availability']
    });

  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Service health check failed'
      },
      { status: 500 }
    );
  }
}
