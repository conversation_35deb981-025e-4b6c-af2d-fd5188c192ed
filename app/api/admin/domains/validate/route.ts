import { NextResponse } from 'next/server';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

// POST /api/admin/domains/validate - Validate domain availability
export async function POST(request: Request) {
  try {
    const { domain, organizationId, excludeDomainId } = await request.json();

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }

    // Call the database function to validate domain
    const { data, error } = await supabaseServiceRole.rpc(
      'validate_domain_for_creation',
      { 
        domain_input: domain.trim(),
        organization_id: organizationId || null,
        exclude_domain_id: excludeDomainId || null
      }
    );

    if (error) {
      console.error('Domain validation error:', error);
      return NextResponse.json(
        { 
          valid: false,
          error: 'validation_error',
          message: 'Unable to validate domain. Please try again.',
          domain: domain.trim()
        },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Domain validation request error:', error);
    return NextResponse.json(
      { 
        valid: false,
        error: 'server_error',
        message: 'Server error occurred. Please try again.',
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/domains/validate - Health check
export async function GET() {
  try {
    // Test database connection and function availability
    const { data, error } = await supabaseServiceRole.rpc(
      'validate_domain_for_creation',
      { domain_input: 'test-domain-health-check.com' }
    );

    if (error) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'Domain validation function not available',
          error: error.message
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      service: 'Domain Validation API',
      timestamp: new Date().toISOString(),
      functions_available: ['validate_domain_for_creation']
    });

  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Service health check failed'
      },
      { status: 500 }
    );
  }
}
