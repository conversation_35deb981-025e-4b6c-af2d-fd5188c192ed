import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';

export async function GET() {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ 
        hasAccess: false, 
        reason: 'not_authenticated' 
      }, { status: 401 });
    }

    // Check if user is an admin in any organization
    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select(`
        role,
        organization_id,
        organizations (
          id,
          name,
          slug
        )
      `)
      .eq('user_id', user.id)
      .eq('role', 'admin')
      .single();

    if (membershipError || !membership) {
      return NextResponse.json({ 
        hasAccess: false, 
        reason: 'not_admin',
        message: 'User does not have admin privileges'
      });
    }

    return NextResponse.json({
      hasAccess: true,
      user: {
        id: user.id,
        email: user.email
      },
      organization: membership.organizations,
      role: membership.role
    });

  } catch (error) {
    console.error('Admin access check error:', error);
    return NextResponse.json(
      { 
        hasAccess: false, 
        reason: 'server_error',
        message: 'Internal server error'
      },
      { status: 500 }
    );
  }
}
