import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

// GET /api/admin/impersonate/status - Get current impersonation status
export async function GET() {
  try {
    const cookieStore = await cookies()
    
    const isActive = cookieStore.get('impersonation_active')?.value === 'true'
    const impersonatedUserId = cookieStore.get('impersonated_user_id')?.value
    const originalAdminId = cookieStore.get('original_admin_id')?.value

    if (!isActive || !impersonatedUserId || !originalAdminId) {
      return NextResponse.json({
        isImpersonating: false,
        impersonatedUser: null,
        originalAdmin: null
      })
    }

    // Get impersonated user details
    const { data: impersonatedUser } = await supabaseServiceRole
      .from('organization_members')
      .select(`
        user_id,
        role,
        organization_id,
        auth.users (
          id,
          email
        )
      `)
      .eq('user_id', impersonatedUserId)
      .single()

    // Get original admin details
    const { data: originalAdmin } = await supabaseServiceRole
      .from('auth.users')
      .select('id, email')
      .eq('id', originalAdminId)
      .single()

    return NextResponse.json({
      isImpersonating: true,
      impersonatedUser: impersonatedUser ? {
        id: impersonatedUser.user_id,
        email: impersonatedUser['auth.users']?.email,
        role: impersonatedUser.role,
        organization_id: impersonatedUser.organization_id
      } : null,
      originalAdmin: originalAdmin ? {
        id: originalAdmin.id,
        email: originalAdmin.email
      } : null
    })

  } catch (error) {
    console.error('Get impersonation status error:', error)
    return NextResponse.json(
      { error: 'Failed to get impersonation status' },
      { status: 500 }
    )
  }
}
