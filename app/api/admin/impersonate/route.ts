import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

// POST /api/admin/impersonate - Start impersonating a user
export async function POST(request: NextRequest) {
  try {
    const { targetUserId } = await request.json()

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'Target user ID is required' },
        { status: 400 }
      )
    }

    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Server component limitation
            }
          },
        },
      }
    )

    // Get the current admin user
    const { data: { user: adminUser }, error: adminError } = await supabase.auth.getUser()
    if (adminError || !adminUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify admin privileges
    const { data: adminMembership } = await supabase
      .from('organization_members')
      .select('role, organization_id')
      .eq('user_id', adminUser.id)
      .eq('role', 'admin')
      .single()

    if (!adminMembership) {
      return NextResponse.json(
        { error: 'Admin privileges required' },
        { status: 403 }
      )
    }

    // Get target user details and verify they're in the same organization
    const { data: targetUser, error: targetError } = await supabaseServiceRole
      .from('organization_members')
      .select(`
        user_id,
        role,
        organization_id,
        auth.users (
          id,
          email,
          created_at
        )
      `)
      .eq('user_id', targetUserId)
      .eq('organization_id', adminMembership.organization_id)
      .single()

    if (targetError || !targetUser) {
      return NextResponse.json(
        { error: 'Target user not found in your organization' },
        { status: 404 }
      )
    }

    // Store impersonation session in cookies
    const response = NextResponse.json({
      success: true,
      message: `Now impersonating ${targetUser['auth.users'].email}`,
      impersonatedUser: {
        id: targetUser.user_id,
        email: targetUser['auth.users'].email,
        role: targetUser.role,
        organization_id: targetUser.organization_id
      },
      originalAdmin: {
        id: adminUser.id,
        email: adminUser.email
      }
    })

    // Set impersonation cookies
    response.cookies.set('impersonation_active', 'true', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 // 24 hours
    })

    response.cookies.set('impersonated_user_id', targetUserId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 // 24 hours
    })

    response.cookies.set('original_admin_id', adminUser.id, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 // 24 hours
    })

    return response

  } catch (error) {
    console.error('Impersonation error:', error)
    return NextResponse.json(
      { error: 'Failed to start impersonation' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/impersonate - Stop impersonating
export async function DELETE() {
  try {
    const response = NextResponse.json({
      success: true,
      message: 'Impersonation stopped'
    })

    // Clear impersonation cookies
    response.cookies.delete('impersonation_active')
    response.cookies.delete('impersonated_user_id')
    response.cookies.delete('original_admin_id')

    return response

  } catch (error) {
    console.error('Stop impersonation error:', error)
    return NextResponse.json(
      { error: 'Failed to stop impersonation' },
      { status: 500 }
    )
  }
}
