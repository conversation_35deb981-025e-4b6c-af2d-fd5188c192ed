import { NextResponse } from 'next/server';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

// This is a temporary endpoint to manually handle user signup and organization assignment
export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    console.log('Manual signup for:', email);

    // Extract domain from email
    const domain = email.toLowerCase().split('@')[1];
    if (!domain) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate domain is allowed
    const { data: domainRecord } = await supabaseServiceRole
      .from('organization_domains')
      .select(`
        organization_id,
        auto_assign_role,
        organizations (
          id,
          name
        )
      `)
      .eq('domain', domain)
      .eq('verified', true)
      .eq('admin_approved', true)
      .single();

    if (!domainRecord) {
      return NextResponse.json({
        allowed: false,
        reason: 'domain_not_approved',
        message: 'Your organization domain is not registered or approved.',
        domain
      });
    }

    // Create user in Supabase Auth using admin API
    const { data: authData, error: authError } = await supabaseServiceRole.auth.admin.createUser({
      email,
      email_confirm: true, // Skip email confirmation for development
    });

    if (authError) {
      console.error('Error creating user:', authError);
      return NextResponse.json(
        { error: 'Failed to create user: ' + authError.message },
        { status: 500 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'User creation failed' },
        { status: 500 }
      );
    }

    // Assign user to organization
    const { data: membership, error: membershipError } = await supabaseServiceRole
      .from('organization_members')
      .insert({
        organization_id: domainRecord.organization_id,
        user_id: authData.user.id,
        role: domainRecord.auto_assign_role || 'member',
        joined_at: new Date().toISOString()
      })
      .select()
      .single();

    if (membershipError) {
      console.error('Error creating organization membership:', membershipError);
      // Don't fail completely, user is created but not assigned
    }

    // Generate a magic link for the user
    const { data: linkData, error: linkError } = await supabaseServiceRole.auth.admin.generateLink({
      type: 'magiclink',
      email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/auth/callback`
      }
    });

    if (linkError) {
      console.error('Error generating magic link:', linkError);
      return NextResponse.json(
        { error: 'User created but failed to generate magic link' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'User created and assigned to organization',
      user: {
        id: authData.user.id,
        email: authData.user.email
      },
      organization: domainRecord.organizations,
      membership,
      magicLink: linkData.properties?.action_link,
      instructions: 'Click the magic link to sign in directly'
    });

  } catch (error) {
    console.error('Manual signup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
