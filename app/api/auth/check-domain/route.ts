import { NextRequest, NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Extract and normalize domain from email
    const domain = email.toLowerCase().trim().split('@')[1]
    if (!domain) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Normalize domain (remove www, etc.)
    const { data: normalizedResult, error: normalizeError } = await supabaseServiceRole.rpc(
      'normalize_domain',
      { domain_input: domain }
    );

    const normalizedDomain = normalizeError ? domain : normalizedResult;

    console.log('Checking domain authorization for:', normalizedDomain)

    // Check if domain exists in organization_domains table
    const { data: authorizedDomain, error } = await supabaseServiceRole
      .from('organization_domains')
      .select(`
        domain,
        verified,
        admin_approved,
        organization:organizations(
          id,
          name
        )
      `)
      .eq('domain', normalizedDomain)
      .eq('verified', true)
      .eq('admin_approved', true)
      .single()

    if (error) {
      console.log('Domain not found or error:', error.message)
      return NextResponse.json({
        authorized: false,
        domain: normalizedDomain,
        message: 'Domain not authorized'
      })
    }

    if (authorizedDomain) {
      console.log('Domain authorized:', authorizedDomain)
      return NextResponse.json({
        authorized: true,
        domain: normalizedDomain,
        organizationId: authorizedDomain.organization.id,
        organizationName: authorizedDomain.organization.name
      })
    }

    return NextResponse.json({
      authorized: false,
      domain: normalizedDomain,
      message: 'Domain not authorized'
    })

  } catch (err: any) {
    console.error('Domain check error:', err)
    return NextResponse.json(
      { error: 'Failed to check domain authorization' },
      { status: 500 }
    )
  }
}
