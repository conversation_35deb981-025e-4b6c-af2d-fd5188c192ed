import { NextResponse } from 'next/server';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

export async function POST(request: Request) {
  try {
    const { userId, email } = await request.json();

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'User ID and email are required' },
        { status: 400 }
      );
    }

    console.log('Ensuring organization assignment for:', { userId, email });

    // Extract domain from email
    const domain = email.toLowerCase().split('@')[1];
    if (!domain) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check if user is already assigned to an organization
    const { data: existingMembership } = await supabaseServiceRole
      .from('organization_members')
      .select('id, organization_id, role')
      .eq('user_id', userId)
      .single();

    if (existingMembership) {
      console.log('User already has organization membership:', existingMembership);
      return NextResponse.json({
        success: true,
        message: 'User already assigned to organization',
        membership: existingMembership
      });
    }

    // Find the organization for this domain
    const { data: domainRecord } = await supabaseServiceRole
      .from('organization_domains')
      .select(`
        organization_id,
        auto_assign_role,
        organizations (
          id,
          name
        )
      `)
      .eq('domain', domain)
      .eq('verified', true)
      .eq('admin_approved', true)
      .single();

    if (!domainRecord) {
      console.log('No approved domain found for:', domain);
      return NextResponse.json(
        { error: 'Domain not found or not approved' },
        { status: 404 }
      );
    }

    // Assign user to organization
    const { data: newMembership, error: membershipError } = await supabaseServiceRole
      .from('organization_members')
      .insert({
        organization_id: domainRecord.organization_id,
        user_id: userId,
        role: domainRecord.auto_assign_role || 'member',
        joined_at: new Date().toISOString()
      })
      .select()
      .single();

    if (membershipError) {
      console.error('Error creating organization membership:', membershipError);
      return NextResponse.json(
        { error: 'Failed to assign user to organization' },
        { status: 500 }
      );
    }

    console.log('Successfully assigned user to organization:', newMembership);

    return NextResponse.json({
      success: true,
      message: 'User successfully assigned to organization',
      membership: newMembership,
      organization: domainRecord.organizations
    });

  } catch (error) {
    console.error('Organization assignment error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
