import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

export async function POST(request: NextRequest) {
  try {
    const { token, type } = await request.json()

    if (!token || !type) {
      return NextResponse.json(
        { error: 'Token and type are required' },
        { status: 400 }
      )
    }

    console.log('Server-side token verification:', { token, type })

    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Server component limitation
            }
          },
        },
      }
    )

    // Try verification with regular client
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: type
    })

    console.log('Regular client verification result:', { 
      hasData: !!data, 
      hasUser: !!data?.user,
      error: error?.message 
    })

    if (error) {
      // Try with service role client
      console.log('Trying with service role client...')
      
      const { data: serviceData, error: serviceError } = await supabaseServiceRole.auth.verifyOtp({
        token_hash: token,
        type: type
      })

      console.log('Service role verification result:', { 
        hasData: !!serviceData, 
        hasUser: !!serviceData?.user,
        error: serviceError?.message 
      })

      if (serviceError) {
        return NextResponse.json(
          { 
            error: `Verification failed: ${error.message}. Service role also failed: ${serviceError.message}`,
            details: { regularError: error, serviceError }
          },
          { status: 400 }
        )
      }

      return NextResponse.json({
        success: true,
        user: serviceData?.user,
        session: serviceData?.session
      })
    }

    return NextResponse.json({
      success: true,
      user: data?.user,
      session: data?.session
    })

  } catch (err: any) {
    console.error('Token verification exception:', err)
    return NextResponse.json(
      { error: `Server error: ${err.message}` },
      { status: 500 }
    )
  }
}
