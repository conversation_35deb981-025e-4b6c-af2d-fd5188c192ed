import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { 
  AgentWizardData, 
  OptimizedAgentConfig, 
  createOpenAIPayload, 
  validateOptimizedConfig 
} from '@/lib/openai-agent-optimizer';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: Request) {
  try {
    // Validate API key
    if (!process.env.OPENAI_API_KEY) {
      console.error('OpenAI API key not configured');
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Parse request body
    const wizardData: AgentWizardData = await request.json();

    // Validate required fields
    if (!wizardData.name || !wizardData.voiceId) {
      return NextResponse.json(
        { error: 'Agent name and voice ID are required' },
        { status: 400 }
      );
    }

    console.log('Processing agent optimization request for:', wizardData.name);
    console.log('Selected template:', wizardData.selectedTemplate?.name);
    console.log('Voice ID:', wizardData.voiceId);

    // Create OpenAI payload
    const openaiPayload = createOpenAIPayload(wizardData);

    // Call OpenAI API
    console.log('Sending request to OpenAI...');
    const completion = await openai.chat.completions.create(openaiPayload);

    // Extract response content
    const responseContent = completion.choices[0]?.message?.content;
    
    if (!responseContent) {
      throw new Error('No response content from OpenAI');
    }

    console.log('Received response from OpenAI');

    // Parse JSON response
    let optimizedConfig: OptimizedAgentConfig;
    try {
      optimizedConfig = JSON.parse(responseContent);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response as JSON:', parseError);
      console.error('Raw response:', responseContent);
      throw new Error('Invalid JSON response from OpenAI');
    }

    // Validate response structure
    if (!validateOptimizedConfig(optimizedConfig)) {
      console.error('Invalid response structure from OpenAI:', optimizedConfig);
      throw new Error('Invalid response structure from OpenAI');
    }

    // Ensure voice_id is preserved exactly as provided
    optimizedConfig.agent.voice_id = wizardData.voiceId;

    // Add usage statistics
    const usage = completion.usage;
    console.log('OpenAI API usage:', {
      prompt_tokens: usage?.prompt_tokens,
      completion_tokens: usage?.completion_tokens,
      total_tokens: usage?.total_tokens
    });

    // Return optimized configuration
    return NextResponse.json({
      success: true,
      optimizedConfig,
      usage: {
        prompt_tokens: usage?.prompt_tokens || 0,
        completion_tokens: usage?.completion_tokens || 0,
        total_tokens: usage?.total_tokens || 0
      },
      originalData: {
        name: wizardData.name,
        template: wizardData.selectedTemplate?.name,
        voiceId: wizardData.voiceId
      }
    });

  } catch (error) {
    console.error('Error in OpenAI agent optimization:', error);

    // Handle specific OpenAI errors
    if (error instanceof OpenAI.APIError) {
      console.error('OpenAI API Error:', {
        status: error.status,
        message: error.message,
        code: error.code,
        type: error.type
      });

      return NextResponse.json(
        { 
          error: 'OpenAI API error',
          details: error.message,
          code: error.code
        },
        { status: error.status || 500 }
      );
    }

    // Handle rate limiting
    if (error instanceof OpenAI.RateLimitError) {
      console.error('OpenAI Rate Limit Error:', error.message);
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          details: 'Please try again in a moment'
        },
        { status: 429 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      { 
        error: 'Failed to optimize agent configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'OpenAI API key not configured'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      service: 'OpenAI Agent Optimizer',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Service health check failed'
      },
      { status: 500 }
    );
  }
}
