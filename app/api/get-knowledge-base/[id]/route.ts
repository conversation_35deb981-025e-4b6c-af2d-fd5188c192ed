import { NextResponse } from 'next/server'
import { getKnowledgeBase } from '@/lib/retell'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const data = await getKnowledgeBase(params.id)
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching knowledge base:', error)
    return NextResponse.json(
      { error: 'Failed to fetch knowledge base' },
      { status: 500 }
    )
  }
}