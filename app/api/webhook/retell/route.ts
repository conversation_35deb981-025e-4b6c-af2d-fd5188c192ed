import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Use service role key for webhook to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!, // Make sure to add this to your .env
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: Request) {
  try {
    // Verify webhook signature if RetellAI provides one
    // const signature = request.headers.get('x-retell-signature')
    
    const payload = await request.json()

    // Call the database function we created in our migration
    const { data, error } = await supabase
      .rpc('handle_call_webhook', {
        payload
      })

    if (error) {
      console.error('Error processing webhook:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true, call_id: data })
  } catch (err) {
    console.error('Webhook error:', err)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 