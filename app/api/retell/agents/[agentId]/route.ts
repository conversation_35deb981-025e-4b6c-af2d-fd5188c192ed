import { NextResponse } from 'next/server'
import { getAgentDetails, updateAgent, deleteAgent } from '@/lib/retell'

// GET /api/retell/agents/[agentId] - Get agent details
export async function GET(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    const agent = await getAgentDetails(params.agentId)
    return NextResponse.json(agent)
  } catch (error) {
    console.error('Error fetching agent:', error)
    return NextResponse.json(
      { error: 'Failed to fetch agent' },
      { status: 500 }
    )
  }
}

// PUT /api/retell/agents/[agentId] - Update agent
export async function PUT(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    const body = await request.json()
    const updatedAgent = await updateAgent(params.agentId, body)
    return NextResponse.json(updatedAgent)
  } catch (error) {
    console.error('Error updating agent:', error)
    return NextResponse.json(
      { error: 'Failed to update agent' },
      { status: 500 }
    )
  }
}

// DELETE /api/retell/agents/[agentId] - Delete agent
export async function DELETE(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    await deleteAgent(params.agentId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting agent:', error)
    return NextResponse.json(
      { error: 'Failed to delete agent' },
      { status: 500 }
    )
  }
}
