import { NextResponse } from 'next/server'
import { 
  listAgents, 
  createAgent, 
  updateAgent, 
  deleteAgent,
  listRetellLLMs,
  createRetellLLM 
} from '@/lib/retell'

// GET /api/retell/agents - List all agents
export async function GET() {
  try {
    const agents = await listAgents()
    return NextResponse.json(agents)
  } catch (error) {
    console.error('Error fetching agents:', error)
    return NextResponse.json(
      { error: 'Failed to fetch agents' },
      { status: 500 }
    )
  }
}

// POST /api/retell/agents - Create a new agent
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { 
      agent_name, 
      voice_id, 
      general_prompt, 
      begin_message,
      language = 'en-US',
      webhook_url 
    } = body

    // Validate required fields
    if (!agent_name || !voice_id) {
      return NextResponse.json(
        { error: 'agent_name and voice_id are required' },
        { status: 400 }
      )
    }

    // Get or create an LLM for the agent
    let llmId: string
    
    try {
      const llms = await listRetellLLMs()
      
      if (llms && llms.length > 0) {
        // Use existing LLM
        llmId = llms[0].llm_id
      } else {
        // Create a new LLM
        const newLLM = await createRetellLLM({
          general_prompt: general_prompt || "You are a helpful AI assistant.",
          begin_message: begin_message || `Hello! I'm ${agent_name}. How can I help you today?`,
          model: "gpt-4o-mini"
        })
        llmId = newLLM.llm_id
      }
    } catch (llmError) {
      console.error('Error handling LLM:', llmError)
      return NextResponse.json(
        { error: 'Failed to create or retrieve LLM' },
        { status: 500 }
      )
    }

    // Create the agent
    const agent = await createAgent({
      agent_name,
      voice_id,
      response_engine: {
        type: 'retell-llm',
        llm_id: llmId
      },
      language,
      webhook_url: webhook_url || `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhook/retell`
    })

    return NextResponse.json(agent)
  } catch (error) {
    console.error('Error creating agent:', error)
    return NextResponse.json(
      { error: 'Failed to create agent' },
      { status: 500 }
    )
  }
}

// PUT /api/retell/agents - Update an agent
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { agent_id, ...updates } = body

    if (!agent_id) {
      return NextResponse.json(
        { error: 'agent_id is required' },
        { status: 400 }
      )
    }

    const updatedAgent = await updateAgent(agent_id, updates)
    return NextResponse.json(updatedAgent)
  } catch (error) {
    console.error('Error updating agent:', error)
    return NextResponse.json(
      { error: 'Failed to update agent' },
      { status: 500 }
    )
  }
}

// DELETE /api/retell/agents - Delete an agent
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const agent_id = searchParams.get('agent_id')

    if (!agent_id) {
      return NextResponse.json(
        { error: 'agent_id is required' },
        { status: 400 }
      )
    }

    await deleteAgent(agent_id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting agent:', error)
    return NextResponse.json(
      { error: 'Failed to delete agent' },
      { status: 500 }
    )
  }
}
