import { NextResponse } from 'next/server'
import { 
  listPhoneNumbers, 
  createPhoneNumber, 
  updatePhoneNumber, 
  deletePhoneNumber 
} from '@/lib/retell'

// GET /api/retell/phone-numbers - List all phone numbers
export async function GET() {
  try {
    const phoneNumbers = await listPhoneNumbers()
    return NextResponse.json(phoneNumbers)
  } catch (error) {
    console.error('Error fetching phone numbers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch phone numbers' },
      { status: 500 }
    )
  }
}

// POST /api/retell/phone-numbers - Create a new phone number
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { area_code, agent_id } = body

    if (!area_code) {
      return NextResponse.json(
        { error: 'area_code is required' },
        { status: 400 }
      )
    }

    const phoneNumber = await createPhoneNumber(area_code, agent_id)
    return NextResponse.json(phoneNumber)
  } catch (error) {
    console.error('Error creating phone number:', error)
    return NextResponse.json(
      { error: 'Failed to create phone number' },
      { status: 500 }
    )
  }
}

// PUT /api/retell/phone-numbers - Update a phone number
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { phone_number, ...updates } = body

    if (!phone_number) {
      return NextResponse.json(
        { error: 'phone_number is required' },
        { status: 400 }
      )
    }

    const updatedPhoneNumber = await updatePhoneNumber(phone_number, updates)
    return NextResponse.json(updatedPhoneNumber)
  } catch (error) {
    console.error('Error updating phone number:', error)
    return NextResponse.json(
      { error: 'Failed to update phone number' },
      { status: 500 }
    )
  }
}

// DELETE /api/retell/phone-numbers - Delete a phone number
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const phone_number = searchParams.get('phone_number')

    if (!phone_number) {
      return NextResponse.json(
        { error: 'phone_number is required' },
        { status: 400 }
      )
    }

    await deletePhoneNumber(phone_number)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting phone number:', error)
    return NextResponse.json(
      { error: 'Failed to delete phone number' },
      { status: 500 }
    )
  }
}
