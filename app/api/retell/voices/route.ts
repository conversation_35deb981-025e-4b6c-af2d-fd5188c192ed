import { NextResponse } from 'next/server'
import { listVoices, getVoice } from '@/lib/retell'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

// GET /api/retell/voices - List all available voices
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const voiceId = searchParams.get('voice_id')

    if (voiceId) {
      // Get specific voice
      const voice = await getVoice(voiceId)
      return NextResponse.json(voice)
    } else {
      // List all voices
      const voices = await listVoices()
      return NextResponse.json(voices)
    }
  } catch (error) {
    console.error('Error fetching voices:', error)
    return NextResponse.json(
      { error: 'Failed to fetch voices' },
      { status: 500 }
    )
  }
}
