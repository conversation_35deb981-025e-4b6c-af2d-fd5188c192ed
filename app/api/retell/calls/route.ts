import { NextResponse } from 'next/server'
import { 
  listCalls, 
  createPhoneCall, 
  createWebCall 
} from '@/lib/retell'

// GET /api/retell/calls - List calls with optional filters
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params: any = {}
    
    // Extract query parameters
    if (searchParams.get('start_timestamp')) {
      params.start_timestamp = searchParams.get('start_timestamp')
    }
    if (searchParams.get('end_timestamp')) {
      params.end_timestamp = searchParams.get('end_timestamp')
    }
    if (searchParams.get('limit')) {
      params.limit = parseInt(searchParams.get('limit') || '10')
    }
    if (searchParams.get('offset')) {
      params.offset = parseInt(searchParams.get('offset') || '0')
    }

    const calls = await listCalls(params)
    return NextResponse.json(calls)
  } catch (error) {
    console.error('Error fetching calls:', error)
    return NextResponse.json(
      { error: 'Failed to fetch calls' },
      { status: 500 }
    )
  }
}

// POST /api/retell/calls - Create a new call (phone or web)
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { type, ...callParams } = body

    if (!type || !['phone', 'web'].includes(type)) {
      return NextResponse.json(
        { error: 'type must be either "phone" or "web"' },
        { status: 400 }
      )
    }

    let call
    
    if (type === 'phone') {
      const { from_number, to_number, override_agent_id, retell_llm_dynamic_variables, metadata } = callParams
      
      if (!from_number || !to_number) {
        return NextResponse.json(
          { error: 'from_number and to_number are required for phone calls' },
          { status: 400 }
        )
      }

      call = await createPhoneCall({
        from_number,
        to_number,
        override_agent_id,
        retell_llm_dynamic_variables,
        metadata
      })
    } else {
      const { agent_id, retell_llm_dynamic_variables, metadata } = callParams
      
      if (!agent_id) {
        return NextResponse.json(
          { error: 'agent_id is required for web calls' },
          { status: 400 }
        )
      }

      call = await createWebCall({
        agent_id,
        retell_llm_dynamic_variables,
        metadata
      })
    }

    return NextResponse.json(call)
  } catch (error) {
    console.error('Error creating call:', error)
    return NextResponse.json(
      { error: 'Failed to create call' },
      { status: 500 }
    )
  }
}
