import { NextResponse } from 'next/server'
import { 
  listRetellLLMs,
  createRetellLL<PERSON>,
  updateRetellLL<PERSON>,
  deleteRetellLL<PERSON>
} from '@/lib/retell'

// GET /api/retell/llms - List all LLMs
export async function GET() {
  try {
    const llms = await listRetellLLMs()
    return NextResponse.json(llms)
  } catch (error) {
    console.error('Error fetching LLMs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch LLMs' },
      { status: 500 }
    )
  }
}

// POST /api/retell/llms - Create a new LLM
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { 
      general_prompt,
      begin_message,
      model = 'gpt-4o-mini',
      general_tools,
      model_temperature = 0.7,
      tool_call_strict_mode = false
    } = body

    // Validate required fields
    if (!general_prompt) {
      return NextResponse.json(
        { error: 'general_prompt is required' },
        { status: 400 }
      )
    }

    // Create the LLM
    const llm = await createRetellLLM({
      general_prompt,
      begin_message: begin_message || "Hello! How can I help you today?",
      model,
      general_tools: general_tools || [],
      model_temperature,
      tool_call_strict_mode
    })

    return NextResponse.json(llm)
  } catch (error) {
    console.error('Error creating LLM:', error)
    return NextResponse.json(
      { error: 'Failed to create LLM' },
      { status: 500 }
    )
  }
}

// PUT /api/retell/llms - Update an LLM
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { llm_id, ...updates } = body

    if (!llm_id) {
      return NextResponse.json(
        { error: 'llm_id is required' },
        { status: 400 }
      )
    }

    const updatedLLM = await updateRetellLLM(llm_id, updates)
    return NextResponse.json(updatedLLM)
  } catch (error) {
    console.error('Error updating LLM:', error)
    return NextResponse.json(
      { error: 'Failed to update LLM' },
      { status: 500 }
    )
  }
}

// DELETE /api/retell/llms - Delete an LLM
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const llm_id = searchParams.get('llm_id')

    if (!llm_id) {
      return NextResponse.json(
        { error: 'llm_id is required' },
        { status: 400 }
      )
    }

    await deleteRetellLLM(llm_id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting LLM:', error)
    return NextResponse.json(
      { error: 'Failed to delete LLM' },
      { status: 500 }
    )
  }
}
