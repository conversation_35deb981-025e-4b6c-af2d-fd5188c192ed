import { NextResponse } from 'next/server'
import { listKnowledgeBases } from '@/lib/retell'

export async function GET() {
  try {
    const data = await listKnowledgeBases()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching knowledge bases:', error)
    return NextResponse.json(
      { error: 'Failed to fetch knowledge bases' },
      { status: 500 }
    )
  }
}