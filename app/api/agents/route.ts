import { NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

// GET /api/agents - Get all active agents (bypasses RLS)
export async function GET() {
  try {
    const { data, error } = await supabaseServiceRole
      .from('organization_agents')
      .select('name, agent_id, type, status')
      .eq('status', 'active')
      .order('name')

    if (error) {
      console.error('Error fetching agents:', error)
      return NextResponse.json(
        { error: 'Failed to fetch agents' },
        { status: 500 }
      )
    }

    return NextResponse.json(data || [])
  } catch (error) {
    console.error('Error in GET /api/agents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
