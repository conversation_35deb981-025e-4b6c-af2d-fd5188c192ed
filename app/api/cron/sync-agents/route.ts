import { NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

// This endpoint will be called by a cron job (Vercel Cron, GitHub Actions, etc.)
export async function POST(request: Request) {
  try {
    // Verify cron secret to prevent unauthorized access
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    console.log('🔄 Starting scheduled agent sync...')

    // Get all organizations that need syncing
    const { data: organizations, error: orgError } = await supabaseServiceRole
      .from('organizations')
      .select('id, name, settings')
      .eq('status', 'active')

    if (orgError) {
      throw new Error(`Failed to fetch organizations: ${orgError.message}`)
    }

    const results = []

    for (const org of organizations || []) {
      try {
        console.log(`🔄 Syncing agents for organization: ${org.name}`)

        // Call the sync API for each organization
        const syncResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/sync/agents`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            organization_id: org.id,
            dry_run: false
          })
        })

        const syncResult = await syncResponse.json()

        results.push({
          organization_id: org.id,
          organization_name: org.name,
          success: syncResult.success,
          summary: syncResult.summary,
          timestamp: new Date().toISOString()
        })

        console.log(`✅ Sync completed for ${org.name}:`, syncResult.summary)

      } catch (error) {
        console.error(`❌ Sync failed for organization ${org.name}:`, error)
        results.push({
          organization_id: org.id,
          organization_name: org.name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })
      }
    }

    // Log the overall results
    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    console.log(`🎯 Scheduled sync completed: ${successCount} success, ${failureCount} failures`)

    // Store sync log in database
    await supabaseServiceRole
      .from('sync_logs')
      .insert({
        sync_type: 'agents',
        trigger: 'scheduled',
        results: results,
        success_count: successCount,
        failure_count: failureCount,
        created_at: new Date().toISOString()
      })
      .catch(error => {
        console.error('Failed to log sync results:', error)
      })

    return NextResponse.json({
      success: true,
      message: `Sync completed for ${organizations?.length || 0} organizations`,
      results,
      summary: {
        total_organizations: organizations?.length || 0,
        successful_syncs: successCount,
        failed_syncs: failureCount
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Scheduled sync failed:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// GET endpoint for health check
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    endpoint: 'cron/sync-agents',
    timestamp: new Date().toISOString()
  })
}
