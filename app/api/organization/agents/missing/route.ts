import { NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'
import { listAgents } from '@/lib/retell'

// GET /api/organization/agents/missing - Get RetellAI agents not in organization
export async function GET() {
  try {
    // Get all RetellAI agents
    const retellAgents = await listAgents()
    
    // Get all organization agents
    const { data: orgAgents, error } = await supabaseServiceRole
      .from('organization_agents')
      .select('agent_id')

    if (error) {
      console.error('Error fetching organization agents:', error)
      return NextResponse.json(
        { error: 'Failed to fetch organization agents' },
        { status: 500 }
      )
    }

    const orgAgentIds = new Set(orgAgents?.map(agent => agent.agent_id) || [])
    
    // Find missing agents
    const missingAgents = retellAgents.filter(agent => !orgAgentIds.has(agent.agent_id))

    return NextResponse.json({
      total_retell_agents: retellAgents.length,
      organization_agents: orgAgents?.length || 0,
      missing_agents: missingAgents.length,
      agents: missingAgents
    })
  } catch (error) {
    console.error('Error in GET /api/organization/agents/missing:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
