import { NextResponse } from 'next/server'
import { supabaseServiceRole } from '@/lib/supabase/service-role'
import { listAgents } from '@/lib/retell'

// GET /api/organization/agents - List organization agents
export async function GET() {
  try {
    const { data, error } = await supabaseServiceRole
      .from('organization_agents')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching organization agents:', error)
      return NextResponse.json(
        { error: 'Failed to fetch organization agents' },
        { status: 500 }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in GET /api/organization/agents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/organization/agents - Add agent to organization
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { agent_id, name, type = 'inbound_voice', organization_id } = body

    if (!agent_id || !name || !organization_id) {
      return NextResponse.json(
        { error: 'agent_id, name, and organization_id are required' },
        { status: 400 }
      )
    }

    // Check if agent already exists
    const { data: existingAgent } = await supabaseServiceRole
      .from('organization_agents')
      .select('id')
      .eq('agent_id', agent_id)
      .eq('organization_id', organization_id)
      .single()

    if (existingAgent) {
      return NextResponse.json(
        { error: 'Agent already exists in this organization' },
        { status: 409 }
      )
    }

    // Add agent to organization
    const { data, error } = await supabaseServiceRole
      .from('organization_agents')
      .insert({
        organization_id,
        agent_id,
        name,
        type,
        status: 'active'
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding agent to organization:', error)
      return NextResponse.json(
        { error: 'Failed to add agent to organization' },
        { status: 500 }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in POST /api/organization/agents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/organization/agents/missing - Get RetellAI agents not in organization
export async function GET_MISSING() {
  try {
    // Get all RetellAI agents
    const retellAgents = await listAgents()
    
    // Get all organization agents
    const { data: orgAgents, error } = await supabaseServiceRole
      .from('organization_agents')
      .select('agent_id')

    if (error) {
      console.error('Error fetching organization agents:', error)
      return NextResponse.json(
        { error: 'Failed to fetch organization agents' },
        { status: 500 }
      )
    }

    const orgAgentIds = new Set(orgAgents?.map(agent => agent.agent_id) || [])
    
    // Find missing agents
    const missingAgents = retellAgents.filter(agent => !orgAgentIds.has(agent.agent_id))

    return NextResponse.json(missingAgents)
  } catch (error) {
    console.error('Error in GET_MISSING /api/organization/agents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
