import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

// GET /api/organization/current - Get the current user's organization
export async function GET() {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    // For development: if no user is authenticated, return default organization
    if (userError || !user) {
      console.log('No authenticated user found, using default organization for development')
      const defaultOrgId = process.env.DEFAULT_ORGANIZATION_ID || 'd0816f44-4576-4b6f-8c45-a80464f0a6c8'
      return NextResponse.json({
        organization_id: defaultOrgId,
        name: 'Default Organization (Development)',
        isDefault: true,
        isDevelopment: true
      })
    }

    // Get user's organization membership
    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select(`
        organization_id,
        role,
        organizations (
          id,
          name,
          slug,
          plan,
          status
        )
      `)
      .eq('user_id', user.id)
      .single()

    if (membershipError) {
      // If no membership found, return default for development
      console.log('No organization membership found, using default organization')
      const defaultOrgId = process.env.DEFAULT_ORGANIZATION_ID || 'd0816f44-4576-4b6f-8c45-a80464f0a6c8'
      return NextResponse.json({
        organization_id: defaultOrgId,
        name: 'Default Organization',
        isDefault: true
      })
    }

    return NextResponse.json({
      organization_id: membership.organization_id,
      name: membership.organizations.name,
      role: membership.role,
      isDefault: false
    })
  } catch (error) {
    console.error('Error getting current organization:', error)
    return NextResponse.json(
      { error: 'Failed to get current organization' },
      { status: 500 }
    )
  }
}
