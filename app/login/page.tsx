"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Mail, UserPlus, LogIn } from "lucide-react";

export default function LoginPage() {
  const [mode, setMode] = useState<'choose' | 'login' | 'create'>('choose');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setMessage({ type: 'error', text: 'Please enter your email address' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: email.trim(),
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setMessage({ type: 'error', text: error.message });
      } else {
        setMessage({ 
          type: 'success', 
          text: 'Check your email for the magic link to sign in!' 
        });
      }
    } catch (err: any) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = () => {
    router.push('/create-account');
  };

  if (mode === 'choose') {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardHeader className="text-center">
            <CardTitle className="text-white text-2xl">Welcome</CardTitle>
            <p className="text-gray-400">Choose an option to continue</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={() => setMode('login')}
              className="w-full h-12"
              size="lg"
            >
              <LogIn className="h-5 w-5 mr-2" />
              Log In
            </Button>
            
            <Button 
              onClick={handleCreateAccount}
              variant="outline"
              className="w-full h-12"
              size="lg"
            >
              <UserPlus className="h-5 w-5 mr-2" />
              Create Account
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (mode === 'login') {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardHeader className="text-center">
            <CardTitle className="text-white text-2xl">Sign In</CardTitle>
            <p className="text-gray-400">Enter your email to receive a magic link</p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              {message && (
                <Alert className={message.type === 'error' ? 'border-red-800 bg-red-900/20' : 'border-green-800 bg-green-900/20'}>
                  <AlertDescription className={message.type === 'error' ? 'text-red-400' : 'text-green-400'}>
                    {message.text}
                  </AlertDescription>
                </Alert>
              )}

              <div>
                <label className="text-sm text-gray-400 mb-2 block">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="bg-gray-800 border-gray-700 text-white"
                  required
                />
              </div>

              <Button 
                type="submit" 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Mail className="h-4 w-4 mr-2" />
                )}
                Send Magic Link
              </Button>

              <Button 
                type="button"
                variant="ghost"
                onClick={() => setMode('choose')}
                className="w-full text-gray-400"
              >
                Back
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}
